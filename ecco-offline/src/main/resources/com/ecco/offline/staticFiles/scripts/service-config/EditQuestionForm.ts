import BaseAsyncCommandForm from "../cmd-queue/BaseAsyncCommandForm";
import Form from "../controls/Form";
import InputGroup from "../controls/InputGroup";
import TextInput from "../controls/TextInput";
import * as commands from "./commands";
import {apiClient} from "ecco-components";
import {Question as QuestionDto, QuestionGroupAjaxRepository} from "ecco-dto";
import {showFormInModalDom} from "../components/MUIConverterUtils";

var repository = new QuestionGroupAjaxRepository(apiClient);


class EditQuestionForm extends BaseAsyncCommandForm<QuestionDto> {

    public static showInModal(questionId: number) {
        var form = new EditQuestionForm(questionId);
        form.load();
        showFormInModalDom(form);
    }

    private form = new Form().addClass("form-30-50");
    private name = new TextInput("question name");
    private origDto: QuestionDto;

    /** We provide the form with current state, and we get back a CommandQueue
     *  containing the changes to send/apply (if we click done).
     */
    constructor(private questionId: number) {
        super(!questionId ? "add new question" : "edit question");
        this.form
            .append( new InputGroup("question name", this.name) );
    }

    protected fetchViewData(): Promise<QuestionDto> {
        if (this.questionId) {
            return repository.findOneQuestion(this.questionId);
        }
        return Promise.resolve(null);
    }

    protected render(questionDto: QuestionDto) {
        this.origDto = questionDto;

        if (questionDto) {
            this.name.setVal(questionDto.name);
        }

        this.enableSubmit(); // probably want to have this be linked to length of commandQueue (based on event?)
        this.element().empty();
        this.append(this.form);
    }


    protected override submitForm(): Promise<void> {
        var cmd;
        if (this.origDto) {
            cmd = new commands.QuestionChangeCommand("update", null, this.origDto.id)
                .changeName(this.origDto.name, this.name.val());
        }
        else {
            cmd = new commands.QuestionChangeCommand("add", null, null)
                .changeName(null, this.name.val());
        }

        if (cmd.hasChanges()) {
            this.commandQueue.addCommand(cmd);
        }
        return super.submitForm();
    }

}

export default EditQuestionForm;
