import $ = require("jquery");

import {FC, useState, useEffect} from "react";
import * as React from "react";
import ActionButton from "../controls/ActionButton";
import BaseTableRowControl from "../controls/BaseTableRowControl";
import * as SessionDataService from "../feature-config/SessionDataService";
import * as commands from "./commands";
import {StringToObjectMap, EccoDate} from "@eccosolutions/ecco-common";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {CommandAjaxRepository, CommandQueue} from "ecco-commands";
import {datePickerInput,
    dateTimeIso8601Input,
    showReactInModal
} from "ecco-components-core";
import {apiClient} from "ecco-components";
import {ClientAttendanceDto, GroupActivityDto, ResourceList, SessionData} from "ecco-dto";
import PagedAsyncTableControl from "../controls/PagedAsyncTableControl";
import {ServicesContextProvider} from "../offline/ServicesContextProvider";
import {ClientAttendance, GroupActivity, GroupPageType} from "ecco-dto";
import {Grid} from "@eccosolutions/ecco-mui";

const cmdRepository = new CommandAjaxRepository(apiClient);


interface ActivityCopyState {
    cloneDate: string | null;
    endDate: EccoDate | null;
}
interface ActivityCopyProps {
    state: ActivityCopyState;
    stateSetter: (update: ActivityCopyState) => void;
    groupPageType: GroupPageType;
}
// DATE picker - using the whole state object, because that's what datePickerInput does, and string allows typing
const ActivityCopyDate = (props: ActivityCopyProps) => {
    const {state, stateSetter} = props;

    const cloneDateIn = {
        cloneDate: state.cloneDate,
        endDate: state.endDate
    };
    const cloneDateOut = (out: {cloneDate: string, endDate: EccoDate | null}) => {
        stateSetter({
            ...state,
            cloneDate: out.cloneDate,
            endDate: out.endDate
        });
    };

    return (<>
        <Grid item xs={12}>
            {dateTimeIso8601Input(
                "cloneDate",
                "clone to",
                cloneDateOut,
                cloneDateIn,
                false,
                true
            )}
        </Grid>
        {props.groupPageType == "courses" &&
            <Grid item xs={12}>
                {datePickerInput("endDate", "end date", cloneDateOut, cloneDateIn)}
            </Grid>
        }
    </>);
};

// FORM to manage state of the date picker (and other fields)
// NB this doesn't have the 'save' button, so we need to pass back the updated state when it changes
//  but we need to have a state so the form maintains its values (the callee is not react)
const ActivityCopyForm: FC<{maintainState: (state: ActivityCopyState) => void, groupPageType: GroupPageType}> = (props) => {
    const [state, setState] = useState<ActivityCopyState>({cloneDate: null, endDate: null});

    useEffect(() => {
        props.maintainState(state)
    }, [state]);

    return (
        <ActivityCopyDate state={state} stateSetter={setState} groupPageType={props.groupPageType}/>
    );
}


export abstract class GroupRowControl extends BaseTableRowControl<GroupActivityDto> {

    constructor(protected groupPageType: GroupPageType | null, protected activity: GroupActivityDto, protected sessionData: SessionData, private reload: () => void) {
        super(activity);
    }

    // COPY ACTIVITY POPUP - maintaining state and the modal
    private showCopy(onClone: (update: ActivityCopyState | null) => void) {
        let latestState: ActivityCopyState = null;
        const ActivityElm = <ServicesContextProvider>
            <ActivityCopyForm maintainState={(state => latestState = state)} groupPageType={this.groupPageType}/>
        </ServicesContextProvider>
        showReactInModal(`copy ${this.groupPageType == "sessions" ? "session" : "course"}`,
            ActivityElm,
            {
                onAction: () => onClone(latestState),
                maxWidth: "sm"
            });
    }

    protected abstract override getColumnMapping(): StringToObjectMap<(dto: GroupActivityDto) => string|$.JQuery>;
    protected abstract findClientsByActivityId(activityId: number): Promise<ClientAttendanceDto[]>;
    protected abstract findOneActivityByUuid(activityUuid: string): Promise<GroupActivity>;
    protected abstract copyActivityCmd(activityNewUuid: Uuid): commands.GroupActivityCommand;
    protected abstract copyActivityDetailCmds(activityNew: GroupActivity, attendees: ClientAttendance[]);

    private copyActivity(dto: GroupActivityDto, state: ActivityCopyState | null, sessionData: SessionData) {
        if (!state || !state.cloneDate) {
            return Promise.resolve(null);
        }

        // PREPARE: CREATE ACTIVITY
        const activityNewUuid = Uuid.randomV4();
        const activityCmd = this.copyActivityCmd(activityNewUuid)
            .changeDescription(null, dto.description)
            .changeService(null, dto.serviceId)
            .changeProject(null, dto.projectId)
            .changeActivityType(null, dto.activityTypeId)
            .changeVenue(null, dto.venueId)
            .changeCapacity(null, dto.capacity)
            .changeDuration(null, dto.duration)
            .changeStartDateTimeIso(null, state.cloneDate)
            .changeEndDate(null, state.endDate);
        if (this.groupPageType == "courses") {
            activityCmd.asCourse();
        }

        // LOAD dto attendees
        // NB ignore ranges for now
        //const range = GroupSupportOverviewControl.buildDateRange(EccoDateTime.parseIso8601Utc(dto.startDateTime), EccoDate.parseIso8601(dto.endDate));
        const dtoLoaded = this.findClientsByActivityId(dto.id)
            .then( (attendances) => attendances.map( (dto) => new ClientAttendance(dto, sessionData)) )

        // SAVE AND LOAD NEW ACTIVITY
        const activityNew = cmdRepository.sendCommand(activityCmd)
            .then(() => this.findOneActivityByUuid(activityNewUuid.toString())
        );

        return dtoLoaded.then(attendees =>
            activityNew.then(activity => {
                const cmdQueue = new CommandQueue(cmdRepository);
                this.copyActivityDetailCmds(activity, attendees).map(c => cmdQueue.addCommand(c));
                cmdQueue.flushCommands(false)
                    //.then(() => reload);
            }
        ));
    }

    protected adminButtonsFor(item: GroupActivityDto): $.JQuery {
        const divBtns = $("<div>");

        const cloneBtn = new ActionButton("copy")
                .addClass("btn btn-xs btn-info")
                .autoDisable(false);
        cloneBtn.clickSynchronous( () => {
            this.showCopy((onClone: ActivityCopyState | null)  => {
                this.copyActivity(item, onClone, this.sessionData)
                    .then( () => this.reload() );
            })
        });
        divBtns.append(cloneBtn.element());

        // for now can't delete these.
        if (item.clientsAttending + item.clientsAttended > 0) {
            return divBtns;
        }
        if (item.course && item.childrenCount > 0) {
            return divBtns;
        }

        if (this.sessionData.hasRoleAdmin()) {
            const delBtn = new ActionButton("delete", "deleting...").addClass("btn btn-xs btn-warning");
            delBtn.click(() => {
                const cmd = new commands.GroupActivityCommand("remove", Uuid.parse(this.activity.uuid));
                return cmdRepository.sendCommand(cmd)
                        .then(() => {
                            delBtn.setLabel("deleted")
                        });
            });
            divBtns.append(delBtn.element());
        }

        return divBtns;
    }

}

abstract class GroupActivityListControl extends PagedAsyncTableControl<GroupActivityDto> {

    protected sessionData: SessionData; // NOTE: filled in asynchronously - available for render

    constructor(protected groupPageType: GroupPageType | null, protected activityTypeId: number | null = null,
                protected venueId: number | null = null, protected serviceId: number | null = null, protected svcCatId: number | null = null, protected parentId?: number) {
        super("", "", !!parentId);
    }

    protected abstract findActivities(page: number): Promise<ResourceList<GroupActivityDto>>;

    protected fetchViewData(): Promise<GroupActivityDto[]> {
        return this.findActivities(this.page - 1)
            .then( (activities) => {
                return SessionDataService.SessionDataService.getFeatures().then( (sessionData) => {
                    this.sessionData = sessionData;
                    return activities;
                })
            })
            .then ( resourceList => {
                this.hasNext = resourceList.links.some( link => link.rel == "next" );
                this.numPages = resourceList.numPages;
                return resourceList.data;
            }); // Not ideal, but sufficient for now
    }

    protected abstract override getHeaders(); // OVERRIDES default impl

    protected abstract override createRowControl(activity: GroupActivityDto);

    protected override render(activities: GroupActivityDto[]) {
        // render sorted as newest first
        super.render(activities.sort( (a,b) => b.startDateTime.localeCompare(a.startDateTime) ));
    }

    protected getMenu() {
        const $menu = $("<div>")
//        $menu.append( $("<p>")
//            .append("<span>project </span>")
//            .append(serviceProjectSelector.selectProjectElement().addClass("inline")) );


        //events.MenuUpdateEvent.bus.fire( new events.MenuUpdateEvent("nav", $menu) );
        // NB return null to avoid overwriting headers (from PagedAsyncTableControl - see this commit)
        return $menu;
    }

    update(groupPageType: GroupPageType, activityTypeId: number, venueId: number, serviceId: number, svcCatId: number, pageNum: number) {
        this.groupPageType = groupPageType;
        this.page = pageNum
        this.activityTypeId = activityTypeId;
        this.serviceId = serviceId; // beware ... project also needs handling
        this.svcCatId = svcCatId;
        this.venueId = venueId;
        this.load();
    }
}

export default GroupActivityListControl;
