import $ = require("jquery");

import BaseAsyncListControl from "../controls/BaseAsyncListControl";
import BaseListEntryControl from "../controls/BaseListEntryControl";
import {apiClient} from "ecco-components";
import {QuestionGroup, QuestionGroupAjaxRepository} from "ecco-dto";
import EditQuestionGroupForm from "./EditQuestionGroupForm";
import URI from "URI";

var repository = new QuestionGroupAjaxRepository(apiClient);


class EntryControl extends BaseListEntryControl<QuestionGroup> {

    constructor(questionGroup: QuestionGroup) {
        super(questionGroup, "fa fa-pencil");
    }

    protected administerEntry(): void {
        EditQuestionGroupForm.showInModal(this.entry.id);
    }

    protected getEditElement(): $.JQuery {
        let uri = URI(window.location.href)
        return $("<a>").attr("href", `${uri}${this.entry.id}/`).text(this.entry.name)
    }

    protected getEntryIconClass(): string {
        return "fa fa-question";
    }
}

class QuestionGroupsListControl extends BaseAsyncListControl<QuestionGroup> {

    constructor() {
        super("add new question group", "no question groups defined", "fa fa-question");
    }

    protected fetchViewData(): Promise<QuestionGroup[]> {
        return repository.findAllQuestionGroups();
    }
    protected createItemControl(questionGroup: QuestionGroup) {
        return new EntryControl(questionGroup);
    }

    protected addNewEntity() {
        EditQuestionGroupForm.showInModal(null);
    };

}
export default QuestionGroupsListControl;
