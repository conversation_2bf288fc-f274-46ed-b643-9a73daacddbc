import {GroupActivityClientListControl, GroupActivityClientRowControl} from "./GroupActivityClientListControl";
import {ClientAttendanceDto, GroupActivityDto, SessionData} from "ecco-dto";
import {GroupActivityOptions} from "./GroupActivityList";

export class GroupAuxActivityClientListControl extends GroupActivityClientListControl {

    protected getHeaders() {
        let headings = ["client"];
        //headings.push("support note");
        return headings;
    }

    protected createRow(clientAttendance: ClientAttendanceDto) {
        return new AuxClientRowControl(this.sessionData, this.activity, this.options, clientAttendance, this.printable, () => this.$saveButton.prop("disabled", false));
    }
}


class AuxClientRowControl extends GroupActivityClientRowControl {

    constructor(sessionData: SessionData, activity: GroupActivityDto, options: GroupActivityOptions,
                clientAttendance: ClientAttendanceDto, printable: boolean,
                onChangeCallback: () => void) {
        super(sessionData, activity, options, clientAttendance, printable, onChangeCallback);
    }

}
