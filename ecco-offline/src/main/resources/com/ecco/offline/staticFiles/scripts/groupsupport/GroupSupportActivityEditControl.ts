import $ = require("jquery");
import BaseAsyncCommandForm from "../cmd-queue/BaseAsyncCommandForm";
import Alert from "../controls/Alert";
import DateInput from "../controls/DateInput";
import DateTimeInput from "../controls/DateTimeInput";
import Form from "../controls/Form";
import InputGroup from "../controls/InputGroup";
import NumberInput from "../controls/NumberInput";
import TextAreaInput from "../controls/TextAreaInput";
import * as commands from "./commands";

import ActivityTypeSelectorControl from "./controls/ActivityTypeWithDemandSelectorControl";
import GroupSupportVenueSelectorControl from "./controls/GroupSupportVenueSelectorControl";
import ServiceProjectSelectorControl from "./controls/ServiceProjectSelectorControl";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {apiClient} from "ecco-components";
import {showFormInModalDom} from "../components/MUIConverterUtils";
import {GroupActivity} from "ecco-dto";
import {GroupSupportAjaxRepository} from "ecco-dto";

var repository = new GroupSupportAjaxRepository(apiClient);


class GroupSupportActivityEditControl extends BaseAsyncCommandForm<GroupActivity | null> {

    public static showInModal(activityId?: number | undefined, onSave?: (() => void) | undefined) {
        var control = new GroupSupportActivityEditControl(activityId, onSave);
        control.load();
        showFormInModalDom(control);
    }

    // TODO: add tabs as per groupSupportActivity.jsp when groupSupport.new.daysOfWeekReport is enabled
    /*      "edit activity" + "view activity demand"
                    <div class="row">
                        <h1>see here for best days of week</h1>
                    </div>
                    <div class="row">
                        <reports:embedded-chart chartUuid="403252ba-0001-babe-babe-dada7ee1600d"
                            chartClasses="col-xs-12"/>
                    </div>
    */

    private activity: GroupActivity;
    private form = new Form().addClass("form-30-50");
    private description = new TextAreaInput("description");
    private venueSelector = new GroupSupportVenueSelectorControl(null, "");
    private serviceProjectSelector = new ServiceProjectSelectorControl( (val) => this.onServiceSelected(val) );
    private activityTypeSelector = new ActivityTypeSelectorControl();
    private startDateTime = new DateTimeInput(undefined, "startDateTime", true); // no specific zone
    private endDate = new DateInput(undefined, "endDate", true);
    private capacity = new NumberInput("capacity");
    private duration = new NumberInput("duration");

    private projectInputGroup: InputGroup;

    constructor(private activityId?: number | undefined, private onSave?: (() => void) | undefined) {
        super(!activityId ? "add new activity" : "edit activity");

        var $startDateTimeWrapper = $("<div>").addClass("input-group-wrapper");
        $startDateTimeWrapper.append(this.startDateTime.element());
        var $endDateWrapper = $("<div>").addClass("input-group-wrapper");
        $endDateWrapper.append(this.endDate.element());

        this.form
            .append( $("<div>").addClass("alerts-container") )
            .append( new InputGroup("service", this.serviceProjectSelector.selectServiceElement()) )
            .append( this.projectInputGroup = new InputGroup("project", this.serviceProjectSelector.selectProjectElement()) )
            .append( new InputGroup("activity type", this.activityTypeSelector).enableValidation() )
            .append( new InputGroup("description", this.description).enableValidation() )
            .append( new InputGroup("venue", this.venueSelector).enableValidation() )
            .append( new InputGroup("date", $startDateTimeWrapper).enableValidation() )
            .append( new InputGroup("end date", $endDateWrapper) )
            .append( new InputGroup("capacity", this.capacity) )
            .append( new InputGroup("duration (in minutes)", this.duration).enableValidation() );
    }

    protected fetchViewData(): Promise<GroupActivity | null> {
        if (this.activityId) {
            return repository.findOneActivity(this.activityId);
        }
        return Promise.resolve(null);
    }

    protected render(activity: GroupActivity | null) {
        this.activity = activity;

        this.venueSelector.element().addClass("inline");
        this.venueSelector.selectElement().addClass("form-control");
        this.venueSelector.load();

        this.serviceProjectSelector.selectServiceElement().addClass("form-control");
        this.serviceProjectSelector.selectProjectElement().addClass("form-control");
        this.serviceProjectSelector.load();

        this.activityTypeSelector.element().addClass("inline");
        this.activityTypeSelector.selectElement().addClass("form-control");
        this.activityTypeSelector.loadWithService(activity && activity.getDto().serviceId,
            activity && activity.getDto().activityTypeId);

        if (activity) {
            this.description.setVal(activity.getDto().description);
            this.venueSelector.setSelectedId(activity.getDto().venueId);
            this.serviceProjectSelector.setService(activity.getDto().serviceId);
            this.serviceProjectSelector.setProject(activity.getDto().projectId);
            this.startDateTime.setDate(activity.getStartDateTime());
            this.endDate.setDate(activity.getEndDate());
            this.capacity.setVal(activity.getDto().capacity.toString());
            this.duration.setVal(activity.getDto().duration.toString());
        }

        this.modifySubmit({autoDisable: false, disabled: false});
        this.element().empty().append(this.form.element());
    }

    private onServiceSelected(serviceId: number) {
        this.projectInputGroup.element().toggle(this.serviceProjectSelector.hasProjects());

        this.activityTypeSelector.loadWithService(serviceId, this.activity && this.activity.getDto().activityTypeId);
    }

    protected override submitForm(): Promise<void> {
        if (!this.form.isValid()) {
            Alert.dismissableWithTimeout("Please complete all required fields", "alert-warning");
            return Promise.resolve(null);
        }

        var cmd;
        if (this.activity) {
             cmd = new commands.GroupActivityCommand("update", Uuid.parse(this.activity.getDto().uuid))
                .changeDescription(this.activity.getDto().description, this.description.val())
                .changeService(this.activity.getDto().serviceId, this.serviceProjectSelector.getService())
                .changeProject(this.activity.getDto().projectId, this.serviceProjectSelector.getProject())
                .changeActivityType(this.activity.getDto().activityTypeId, this.activityTypeSelector.getSelectedId())
                .changeVenue(this.activity.getDto().venueId, this.venueSelector.getSelectedId())
                .changeCapacity(this.activity.getDto().capacity, parseInt(this.capacity.val()))
                .changeDuration(this.activity.getDto().duration, parseInt(this.duration.val()))
                .changeStartDateTime(this.activity.getStartDateTime(), this.startDateTime.getDate())
                .changeEndDate(this.activity.getEndDate(), this.endDate.getDate());
        } else {
             cmd = new commands.GroupActivityCommand("create", Uuid.randomV4())
                .changeDescription(null, this.description.val())
                .changeService(null, this.serviceProjectSelector.getService())
                .changeProject(null, this.serviceProjectSelector.getProject())
                .changeActivityType(null, this.activityTypeSelector.getSelectedId())
                .changeVenue(null, this.venueSelector.getSelectedId())
                .changeCapacity(null, parseInt(this.capacity.val()))
                .changeDuration(null, parseInt(this.duration.val()))
                .changeStartDateTime(null, this.startDateTime.getDate())
                .changeEndDate(null, this.endDate.getDate())
        }

        if (cmd.hasChanges()) {
            this.commandQueue.addCommand(cmd);
        }

        return super.submitForm().then(() => {
            this.onSave && this.onSave();
        });
    }
}

export default GroupSupportActivityEditControl;
