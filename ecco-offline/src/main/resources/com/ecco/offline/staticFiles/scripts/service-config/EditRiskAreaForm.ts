import BaseAsyncCommandForm from "../cmd-queue/BaseAsyncCommandForm";
import Form from "../controls/Form";
import InputGroup from "../controls/InputGroup";
import TextInput from "../controls/TextInput";
import * as commands from "./commands";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {apiClient} from "ecco-components";
import {showFormInModalDom} from "../components/MUIConverterUtils";
import {OutcomeAjaxRepository} from "./OutcomeAjaxRepository";
import { RiskAreaDto } from 'ecco-dto';

var repository = new OutcomeAjaxRepository(apiClient);


class EditRiskAreaForm extends BaseAsyncCommandForm<RiskAreaDto> {

    public static showInModal(riskAreaId: number) {
        var form = new EditRiskAreaForm(riskAreaId);
        form.load();
        showFormInModalDom(form);
    }

    private form = new Form().addClass("form-30-50");
    private name = new TextInput("risk area name");
    private origDto: RiskAreaDto;

    /** We provide the form with current state, and we get back a CommandQueue
     *  containing the changes to send/apply (if we click done).
     */
    constructor(private riskAreaId: number) {
        super(!riskAreaId ? "add new risk area" : "edit risk area");
        this.form
            .append( new InputGroup("risk area name", this.name) );
    }

    protected fetchViewData(): Promise<RiskAreaDto> {
        if (this.riskAreaId) {
            return repository.findOneRiskArea(this.riskAreaId)
        }
        return Promise.resolve(null);
    }

    protected render(riskArea: RiskAreaDto) {
        this.origDto = riskArea;

        if (riskArea) {
            this.name.setVal(riskArea.name);
        }

        this.enableSubmit(); // probably want to have this be linked to length of commandQueue (based on event?)
        this.element().empty();
        this.append(this.form);
    }


    protected override submitForm(): Promise<void> {
        let cmd;
        if (this.origDto) {
            cmd = new commands.OutcomeDefChangeCommand("update", this.origDto.uuid)
                .changeName(this.origDto.name, this.name.val());
        }
        else {
            cmd = new commands.OutcomeDefChangeCommand("add", Uuid.randomV4().toString(), "THREAT")
                .changeName(null, this.name.val());
        }

        if (cmd.hasChanges()) {
            this.commandQueue.addCommand(cmd);
        }
        return super.submitForm();
    }

}

export default EditRiskAreaForm;
