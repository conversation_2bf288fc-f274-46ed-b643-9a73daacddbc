import {useGroupActivityTypes} from "../groupsSupportHooks";
import {dropdownList} from "ecco-components-core";

interface Props<T> {
    serviceId: number
    label: string
    setter: (newState: T) => void
    state: T
    propertyKey: Extract<keyof T, string>
}

// TODO: Enhance to give functionality of ActivityTypeWithDemandSelectorControl

export function GroupActivityTypeDropdown<T>({serviceId, label, setter, state, propertyKey}: Props<T>) {
    const {activityTypes} = useGroupActivityTypes(serviceId)

    return dropdownList(label, setter, state, propertyKey, activityTypes, {}, !serviceId)
}