import $ = require("jquery");

import BaseAsyncListControl from "../controls/BaseAsyncListControl";
import BaseListEntryControl from "../controls/BaseListEntryControl";
import {apiClient} from "ecco-components";
import {ListDefinitionEntry, SessionDataAjaxRepository} from "ecco-dto";
import {EditListDefForm} from "./EditListDefForm";

const repository: SessionDataAjaxRepository = new SessionDataAjaxRepository(apiClient.withCachePeriod(0));


class EntryControl extends BaseListEntryControl<ListDefinitionEntry> {

    constructor(listDef: ListDefinitionEntry, private fixedListName?: string) {
        super(listDef, "fa fa-pencil", listDef && listDef.getDisabled() ? "disabled" : undefined);
    }

    protected administerEntry(): void {
        EditListDefForm.showInModal(this.entry.getId(), this.fixedListName);
    }

    protected getEditElement(): $.JQuery {
        return $("<div>").css("opacity", this.entry.getDisabled() ? "0.6" : "1").addClass("container-fluid").append($("<div>").addClass("row"))
            .append(
                $("<div>").addClass("col-xs-4").append(
                    $("<span>").css("font-size", "0.8em").text(this.entry.getListName())
                )
            )
            .append(
                $("<div>").addClass("col-xs-6").append(this.entry.getFullName())
            )
            .append(
                $("<div>").addClass("col-xs-1").append(this.entry.getDto().order?.toString())
            )
            .append(
                $("<div>").addClass("col-xs-1").append(
                        '['.concat(this.entry.getId().toString()).concat('] ').concat(
                            this.entry.getDisabled() ? 'disabled ' : ''.concat(
                            this.entry.getDefault() ? 'default' : '')
                        )
                )
            );
    }

    protected getEntryIconClass(): string {
        return "fa fa-list";
    }
}

class ListDefListControl extends BaseAsyncListControl<ListDefinitionEntry> {

    constructor(private fixedListName?: string) {
        super("add new list def", "no list defs defined", "fa fa-list");
    }

    protected fetchViewData(): Promise<ListDefinitionEntry[]> {
        const list = repository.getListDefinitionsOrdered();
        return this.fixedListName
                ? list.then(data => data.filter(ld => ld.getListName() == this.fixedListName))
                : list;
    }
    protected createItemControl(listDef: ListDefinitionEntry) {
        return new EntryControl(listDef, this.fixedListName);
    }

    protected addNewEntity() {
        EditListDefForm.showInModal(null, this.fixedListName);
    };

}
export default ListDefListControl;
