import {applicationRootPath, resourceRootPath} from "application-properties";
import {apiClient} from "ecco-components";
import {link} from "ecco-components-core";
import {Client, isOffline, ReferralAjaxRepository as ReferralRepository, ReferralSummaryDto, SessionData} from "ecco-dto";
import {NewReferralWizard} from "../../referral/components/NewReferralWizard";
import {RowControl} from "../../referral/controls/ReferralsListControl";
import * as React from "react"
import * as ReactDom from "react-dom"
import ReactBootstrap = require("react-bootstrap");
import {SessionDataService} from "../../feature-config/SessionDataService";
import Button = ReactBootstrap.Button;
import Overlay = ReactBootstrap.Overlay;
import Popover = ReactBootstrap.Popover;
import {baseURI} from "../../environment";

const referralRepository = new ReferralRepository(apiClient);

/**
 * Open a referral with consideration for opening the parent if required
 */
// NB other urls are referenced to this function to eventually consolidate
export function openReferral(sessionData: SessionData, referral: ReferralSummaryDto, disableOpenParent = false) {
    if (disableOpenParent) {
        location.href = new URL(getReferralDirectHref(referral.referralId)).href;
    } else {
        getReferralHref(sessionData, referral).then(href => location.href = new URL(href).href);
    }
}

/**
 * If we know the direct referralId (ie without parent consideration etc), then open it
 */
export function openReferralDirect(referralId: number, newTab = false) {
    if (newTab) {
        window.open(getReferralDirectHref(referralId), '_blank');
    } else {
        location.href = getReferralDirectHref(referralId);
    }
}

export function getReferralHref(sessionData: SessionData, referral: ReferralSummaryDto): Promise<string> {
    const svc = sessionData.getServiceCategorisation(referral.serviceAllocationId).serviceId;
    const parameters = sessionData.getService(svc).parameters;
    const openParent = parameters && parameters.openParentReferralInstead;
    if (openParent && referral.parentServiceRecipientId) {
        const referralRepository = new ReferralRepository(apiClient);
        return referralRepository.findOneReferralByServiceRecipientId(referral.parentServiceRecipientId).then(parent => {
            console.debug("referral selected: %o", parent);
            return getReferralDirectHref(parent.referralId);
        });
    } else {
        console.debug("referral selected: %o", referral);
        return Promise.resolve(getReferralDirectHref(referral.referralId));
    }
}

export function getReferralDirectHref(referralId: number) {
    return new URL(`${applicationRootPath}nav/referrals/${referralId}/`, location.href).href;
}
export function getReferralSrIdDirectHref(srId: number) {
    return new URL(`${applicationRootPath}nav/referrals/sr/${srId}/`, location.href).href;
}
export function getNewReferralSrIdDirectHref(srId: number) {
    return new URL(`${applicationRootPath}nav/r/main/sr2/${srId}/`, location.href).href;
}
export function getReferralDirectConditionalHref(referral: {referralId: number, serviceRecipientId: number}, requiresOnline: boolean) {

    // NB requiresOnline is only true for buildings - search 'referralslistcontrol('
    if (requiresOnline || !isOffline()) {
        return `${applicationRootPath}nav/r/main/sr2/${referral.serviceRecipientId}/`
    }

    return baseURI.toString().concat("referrals/" + referral.referralId + "/");

    // r/app/referrals/ (eg ReferralCard and ecco-staff-app/index.html) appears to have some errors showing the newer file (ie preview on)
    //return `${applicationRootPath}r/app/referrals/${referral.serviceRecipientId}/` // see ReferralCard.tsx
}

export function getReferralSrIdHref(sessionData: SessionData, referral: ReferralSummaryDto): string {
    const svc = sessionData.getServiceCategorisation(referral.serviceAllocationId).serviceId;
    const parameters = sessionData.getService(svc).parameters;
    const openParent = parameters && parameters.openParentReferralInstead;
    return (openParent && referral.parentServiceRecipientId)
        ? getReferralSrIdDirectHref(referral.parentServiceRecipientId)
        : getReferralSrIdDirectHref(referral.serviceRecipientId);
}

interface ClientProps {
    client: Client;
    /** Optional href to allow the user to ctrl+click, but using srId to avoid resolving a promise of the referral's parent referralId */
    onSelectedReferralSrIdHref?: ((sessionData: SessionData, referral: ReferralSummaryDto) => string) | undefined;
    onSelectedReferral?: ((sessionData: SessionData, referral: ReferralSummaryDto) => void) | undefined;
    /** Whether to show "new referral" link for new referral modal for this client - default is not to */
    existingOnly?: boolean | undefined;
}

interface ClientState {
    show: boolean;
    referrals?: ReferralSummaryDto[] | undefined;
    showClientLink?: boolean | undefined;
    sessionData: SessionData;
}

/**
 * Renders a button link of the client name, which when clicked shows that client's referrals, and if a referral is clicked,
 * returns the selected referral via the onSelected callback.
 * If showClientLink is set, then there is also the opportunity to go to the client details.
 */
class ClientReferralsPopup extends React.Component<ClientProps, ClientState> {

    private button: HTMLAnchorElement;

    constructor(props, context) {
        super(props, context);

        this.state = {
            show: false,
            referrals: null,
            showClientLink: false,
            sessionData: null
        };
    }

    toggle() {
        this.setState({ show: !this.state.show });

        if (!this.state.referrals) {
            SessionDataService.getFeatures().then(sessionData =>
                // we show all referrals, but readOnly is applied below
                referralRepository.findAllReferralWithoutSecuritySummaryByClient(this.props.client.clientId)
                    .then(referrals =>
                    {
                        this.setState( {
                            sessionData: sessionData,
                            showClientLink: sessionData.isEnabled("welcome.search.clientLink"),
                            referrals: referrals
                        })
                    })
            );
        }
    }

    override render() {
        const {client, onSelectedReferralSrIdHref, onSelectedReferral, existingOnly} = this.props;
        const {show, referrals, showClientLink, sessionData} = this.state;

        return (
            <div key={client.clientId}>
                <a style={{cursor: "pointer"}} ref={ref => this.button = ref} onClick={() => this.toggle()}>
                    {client.firstName} {client.lastName}
                </a>
                <br/>
                <small>{client.code || client.clientId.toString()}</small>
                <Overlay show={show}
                        target={() => ReactDom.findDOMNode(this.button)}
                        placement="bottom">
                    <Popover id={'popover'+client.clientId.toString()} title="" style={{maxWidth: 400}} autoFocus={true}>
                        <div>
                            <i className="pull-right fa fa-close" onClick={() => this.toggle()}/>
                        </div>
                        {!existingOnly && <Button bsSize="medium" bsStyle="link"
                                                     onClick={() => {
                                                         this.toggle();
                                                         NewReferralWizard.popup(client.clientId, r => openReferralDirect(r.referralId));
                                                     }}>
                            new referral</Button>}
                        {showClientLink && (<Button bsSize="sm" bsStyle="link"
                                                onClick={() => window.location.href = "entity/clients/" + client.clientId}>
                            goto client details</Button>)}
                        {referrals && referrals.length == 0 && (<div>no referrals</div>)}
                        {referrals && referrals.length > 0 && (<div>referrals</div>)}
                        {referrals && referrals.map(referral => {
                            const isAccessbile = !referral._readOnly;
                            const SummaryLabel = <>{sessionData.getServiceCategorisation(referral.serviceAllocationId).serviceName} - {RowControl.summariseStatus(referral)} (r-id: {referral.referralId})</>;
                            return (
                                <div key={referral.referralId.toString()}>
                                    {isAccessbile
                                      ? link(SummaryLabel, () => onSelectedReferral(sessionData, referral),
                                                onSelectedReferralSrIdHref && onSelectedReferralSrIdHref(sessionData, referral))
                                      : <span>
                                          {SummaryLabel}
                                        </span>
                                    }
                                </div>
                                );
                        })}
                        {(!referrals) && (<img className="spinner"
                                               src={resourceRootPath + "themes/ecco/images/loading.gif"}
                                               style={{ width: 16, height: 16 }}/>)
                        }
                    </Popover>
                </Overlay>
            </div>
        );
    }

}

export default ClientReferralsPopup;
