import $ = require("jquery");

import BaseAsyncDataControl from "../controls/BaseAsyncDataControl";
import services = require("ecco-offline-data");
import {HateoasResource, StringToObjectMap} from "@eccosolutions/ecco-common";
import {CommandAjaxRepository, EvidenceCommand} from "ecco-commands";
import {apiClient} from "ecco-components";
import {showInModalDom} from "ecco-components-core";
import {
    BaseServiceRecipientCommandDto,
    EvidenceGroup,
    getGlobalApiClient,
    ReferralAjaxRepository,
    ServiceRecipientAjaxRepository,
    ServiceRecipientWithEntities, SessionData
} from "ecco-dto";
import {onParentTabActivated} from "../common/tabEvents";
import {CommandHistoryItemControl} from "./CommandHistoryItemControl";
import {CommandViewHandler} from "ecco-components";
import {SessionDataService} from "../feature-config/SessionDataService";

const commandRepository = new CommandAjaxRepository(apiClient);
const referralRepository = new ReferralAjaxRepository(apiClient);
const serviceRecipientRepository = new ServiceRecipientAjaxRepository(apiClient);

class BackingData {
    constructor(public commands: BaseServiceRecipientCommandDto[],
            public sessionData: SessionData) {
    }
}


abstract class CommandHistoryListControl extends BaseAsyncDataControl<BackingData> {

    // TODO: Delete these and use via AuditHistory.tsx for good services management

    // called by AuditReportStageControl
    public static createWithMultipleEntities(detailPermission: boolean) {
        return new MultipleEntitiesCommandHistoryListControl(detailPermission);
    }

    // called by EvidenceConfirmationStep - sign evidence
    public static createWithEntities(serviceRecipient: ServiceRecipientWithEntities, commands: EvidenceCommand[], detailPermission: boolean) {
        return new EntitiesCommandHistoryListControl(serviceRecipient, commands, detailPermission);
    }

    // NB un-paged called by BuildingOverviewControl, changes-page, EvidenceDelegatingForm (for checklist), also see AuditHistory for other direct calls
    public static createWithIds(serviceRecipientId: number, evidenceGroup: EvidenceGroup, taskName: string | null, detailPermission: boolean) {
        return new RemoteCommandHistoryListControl(serviceRecipientId, evidenceGroup, taskName, detailPermission);
    }
    public static queryWithIds(serviceRecipientId: number, evidenceGroup: EvidenceGroup, taskName: string) {
        return RemoteCommandHistoryListControl.query(serviceRecipientId, evidenceGroup, taskName);
    }

    public static createWithOneId(uuid: string, detailPermission: boolean) {
        return new RemoteCommandHistorySingleControl(uuid, detailPermission);
    }
    public static queryWithOneId(uuid: string) {
        return RemoteCommandHistorySingleControl.query(uuid);
    }

    // NB un-paged but only called by ChecklistEvidenceForm.ts currently, also see AuditHistory for other direct calls
    public static createModalWithIds(serviceRecipientId: number, evidenceGroup: EvidenceGroup, taskName: string, detailPermission: boolean) {
        const form = new RemoteCommandHistoryListControl(serviceRecipientId, evidenceGroup, taskName, detailPermission);
        // we didn't have a header or footer, so just the form
        showInModalDom("history", form.element()[0]);
        form.load();
    }

    public static createFromHateoasResource(resource: HateoasResource, detailPermission: boolean) {
        return new HateoasCommandHistoryListControl(resource, detailPermission);
    }
    public static queryFromHateoasResource(resource: HateoasResource) {
        return HateoasCommandHistoryListControl.query(resource);
    }

    // currently this just acts as a way to get the element of the shared control
    private groupControlBySameTimeUuid: StringToObjectMap<CommandHistoryItemControl>;

    constructor(private detailPermission: boolean) {
        super();
    }

    public setMultipleEntitiesData(sessionData: SessionData, commands: BaseServiceRecipientCommandDto[]) {
    }

    protected override afterAttach() {
        onParentTabActivated(this.element(), () => {
            this.load();
        })
    }

    protected render(data: BackingData) {
        this.groupControlBySameTimeUuid = {};

        const $el = $("<ul>").addClass("entry-list evidence-history list-unstyled");
        if (data.commands.length == 0) {
            $el.append( $("<li>").text("no details yet") );
        }
        else {
            $el.append(
                data.commands.map( command => {
                    let handler = CommandHistoryItemControl.createCommandViewHandler(command, data.sessionData);
                    return this.getGroupedControl(handler.getSameTimeUuid ? handler.getSameTimeUuid() : null,
                            handler, data.sessionData).element();
                })
                .reverse() // reverse after map as we rely on order for latest title
            );
        }
        this.element().empty().append($el);
    }

    /**
     * Create or find an existing control which can be used to render this command.
     * Re-using controls allows us to group related commands for rendering together.
     */
    private getGroupedControl(sameTimeUuid: string | null, handler: CommandViewHandler<any>, sessionData: SessionData): CommandHistoryItemControl {
        let ctl = sameTimeUuid ? this.groupControlBySameTimeUuid[sameTimeUuid] : null;
        if (!ctl) {
            ctl = new CommandHistoryItemControl(sessionData, this.detailPermission);
            if (sameTimeUuid) {
                this.groupControlBySameTimeUuid[sameTimeUuid] = ctl;
            }
        }
        ctl.processHandler(handler);
        return ctl;
    }
}

/**
 * Provides the ability to audit from multiple serviceRecipients
 */
class MultipleEntitiesCommandHistoryListControl extends CommandHistoryListControl {

    private commands: BaseServiceRecipientCommandDto[];
    private sessionData: SessionData;

    public override setMultipleEntitiesData(sessionData: SessionData, commands: BaseServiceRecipientCommandDto[]) {
        this.sessionData = sessionData;
        this.commands = commands;
        this.load();
    }

    protected fetchViewData(): Promise<BackingData> {
        return Promise.resolve(new BackingData(this.commands, this.sessionData));
    }
}

class EntitiesCommandHistoryListControl extends CommandHistoryListControl {
    constructor(private serviceRecipient: ServiceRecipientWithEntities, private commands: EvidenceCommand[], detailPermission: boolean) {
        super(detailPermission);
    }

    protected fetchViewData(): Promise<BackingData> {
        return Promise.resolve(new BackingData(this.commands.map( cmd => <BaseServiceRecipientCommandDto>cmd.toCommandDto()), this.serviceRecipient.features));
    }
}

class RemoteCommandHistorySingleControl extends CommandHistoryListControl {

    constructor(private uuid: string, detailPermission: boolean) {
        super(detailPermission);
    }

    public static query(uuid: string) {
        return commandRepository.findOne(uuid);
    }

    protected fetchViewData() {
        return RemoteCommandHistorySingleControl.query(this.uuid)
                .then( commands => services.getFeatureConfigRepository().getSessionData()
                        .then( sd => new BackingData([commands], sd) )
                );
    }
}

class RemoteCommandHistoryListControl extends CommandHistoryListControl {

    constructor(private serviceRecipientId: number, private evidenceGroup: EvidenceGroup, private taskName: string | null, detailPermission: boolean) {
        super(detailPermission);
    }

    public static query(serviceRecipientId: number, evidenceGroup: EvidenceGroup, taskName: string | null) {
        return taskName
                ? commandRepository.findCommandsByServiceRecipientIdAndEvidenceGroupAndTaskName(
                        serviceRecipientId, evidenceGroup, taskName)
                : evidenceGroup
                        ? commandRepository.findCommandsByServiceRecipientIdAndEvidenceGroup(serviceRecipientId, evidenceGroup)
                        : serviceRecipientRepository.findServiceRecipientCommandsByCreated(serviceRecipientId, null)
                                // was created DESC, so turn into ASC
                                .then(cmds => cmds.reverse())
    }

    protected fetchViewData() {
        return RemoteCommandHistoryListControl.query(this.serviceRecipientId, this.evidenceGroup, this.taskName)
            .then( commands => referralRepository.findOneServiceRecipientWithEntities(this.serviceRecipientId)
                    .then( recipient => new BackingData(commands, recipient.features) )
            );
    }
}

/** For use with HATEOAS rel="audit-history" */
class HateoasCommandHistoryListControl extends CommandHistoryListControl {

    constructor(private resource: HateoasResource, detailPermission: boolean) {
        super(detailPermission);
    }

    public static query(resource: HateoasResource) {
        return getGlobalApiClient().fetchRelation<BaseServiceRecipientCommandDto[]>(resource, "audit-history");
    }

    protected fetchViewData() {
        return HateoasCommandHistoryListControl.query(this.resource)
            .then( commands =>  SessionDataService.getFeatures()
                    .then( features => new BackingData(commands, features) )
            );
    }
}
export default CommandHistoryListControl;
