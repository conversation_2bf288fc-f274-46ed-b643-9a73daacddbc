import * as React from "react";
import {FC} from "react";
import {GroupActivityOverviewModal} from "./GroupActivityOverviewModal";
import {lazyControlWrapper} from "../components/ControlWrapper";
import {useParams} from "react-router";

export const GroupAuxActivityOverviewModal: FC = () => {
    const {activityId} = useParams<{ activityId: string }>();
    const Component = lazyControlWrapper(() => import("./GroupAuxActivityOverviewControl"), parseInt(activityId, 10));

    return <GroupActivityOverviewModal component={Component}/>
}
