import {<PERSON><PERSON><PERSON>, CardsContainer} from "ecco-components";
import * as React from "react";

import {Grid} from '@eccosolutions/ecco-mui';
import {
    TargetScheduleCard,
    TargetScheduleCardData,
    TargetScheduleSource
} from "./targetScheduleDataLoader";
import {ServicesContextProvider} from "../offline/ServicesContextProvider";

function componentFactory(card: CardData) {
    if (card instanceof TargetScheduleCardData) {
        return <Grid item key={'daily-task' + card.dto.schedule.actionInstanceUuid}>
            <TargetScheduleCard schedule={card.dto}/>
        </Grid>;
    }
    return <>{"unknown card"}</>;
}

const sources = [new TargetScheduleSource()]
export function DailyChecksPage() {
    return (
        <ServicesContextProvider>
            <div className="container-fluid top-gap-15">
                <CardsContainer
                    sources={sources}
                    componentFactory={componentFactory}
                />
                {/*<TargetScheduleDataLoader date={"ISO8601ignored"}>{(context: TargetSchedulesPageData) =>
                    context.schedules.map(schedule => <TargetScheduleCard schedule={schedule} key={'caretask-' + schedule.schedule.actionInstanceUuid}/>)
                }</TargetScheduleDataLoader>*/}
            </div>
        </ServicesContextProvider>
    );
}
