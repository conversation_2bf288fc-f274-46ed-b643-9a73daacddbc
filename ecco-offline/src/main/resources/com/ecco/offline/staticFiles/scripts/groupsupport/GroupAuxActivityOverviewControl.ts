import GroupActivityOverviewControl from "./GroupActivityOverviewControl";
import {GroupActivityDto, SessionData} from "ecco-dto";
import {GroupAuxActivityClientListControl} from "./GroupAuxActivityClientListControl";
import {groupAuxActivityOptions} from "./GroupAuxActivityList";

class GroupAuxActivityOverviewControl extends GroupActivityOverviewControl {

    private auxOptions = groupAuxActivityOptions;

    protected options() {
        return this.auxOptions;
    }

    protected label(lookup: string) {
        switch (lookup) {
            case "invited":
                return "register";
            case "attending":
                return "scheduled";
            case "attended":
                return "sent";
            case "attendance": // only when end date, which is no more
                return "attendance";
            case "register":
                return "printable";
            default:
                return lookup;
        }
    }

    protected createClientListControl(sessionData: SessionData, activity: GroupActivityDto) {
        return new GroupAuxActivityClientListControl(sessionData, activity, this.options());
    }

}
export default GroupAuxActivityOverviewControl;