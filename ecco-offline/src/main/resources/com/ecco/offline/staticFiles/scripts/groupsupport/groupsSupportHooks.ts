import {usePromise, useServicesContext} from "ecco-components";
import {GroupSupportAjaxRepository} from "ecco-dto";
import {useMemo} from "react";


export function useGroupActivity(activityId: number) {
    const {apiClient} = useServicesContext()
    const repository = useMemo(() => new GroupSupportAjaxRepository(apiClient), [apiClient])
    const {resolved, error, loading} = usePromise(
        () => repository.findOneActivity(activityId),
        [activityId]);
    return {activity: resolved, error, loading};
}

export function useGroupActivityTypes(serviceId: number) {
    const {apiClient} = useServicesContext()
    const repository = useMemo(() => new GroupSupportAjaxRepository(apiClient), [apiClient])
    const {resolved, error, loading} = usePromise(
        () => serviceId ? repository.findActivityTypesByServiceId(serviceId) : Promise.resolve([]),
        [serviceId]);
    return {activityTypes: resolved, error, loading};
}
