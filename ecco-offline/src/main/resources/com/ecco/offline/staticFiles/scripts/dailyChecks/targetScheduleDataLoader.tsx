import {EvidenceGroup, SessionData} from "ecco-dto";
import {
    AsyncSessionData,
    CardData,
    CardGroup, CardSource,
    CareVisitRoot,
    CareVisitSummaryCard,
    LoadingSpinner, TargetScheduleData, useServicesContext, useWork,
    withSessionData
} from "ecco-components";
import {ErrorBoundary} from "ecco-components-core";
import * as React from "react";
import {FC, Fragment, useMemo} from "react";
import {EccoDate, EccoDateTime} from "@eccosolutions/ecco-common";
import {ReportAjaxRepository} from "ecco-reports";
import {Observable} from "rxjs";
import {filter, flatMap, map} from "rxjs/operators";
import {AsyncFulfilled, createInstance, FulfilledProps} from "react-async";
import {SessionDataService} from "../feature-config/SessionDataService";
import {getReferralRepository, getSupportSmartStepsSnapshotRepository, stringifyPossibleError} from "ecco-offline-data";
import {transformToCareVisit, EventBackingData} from "ecco-components";
import {Uuid} from "@eccosolutions/ecco-crypto";


/**
 * DATA STRUCTURE
 */
export interface TargetSchedulesPageData extends TargetSchedulesData {
    sessionData: SessionData;
}



/**
 * LOAD DATA
 */
// The react-async 'createInstance' approach to loading data.
// NB this is very repetitive for each bit of data
// NB Context is used as State too

// Schedule data only that is loaded here
interface TargetSchedulesData {
    schedules: TargetScheduleData[];
}

// NB this would be better as a server-side look for target dates, but the approach here should also be easier offline
// see careSingleVisitDataLoader.tsx 'tasksLoad' for how the care app does it
// this is loaded from TargetScheduleSource which is used in DailyChecksPage
// loads live referrals targetSchedules due
function loader(props: {date: string}): Promise<TargetSchedulesData> {
    const filterBefore = EccoDate.todayLocalTime().addDays(1).toDateTimeMidnight();
    const referralsQ = getReferralRepository().findAllReferralSummary(ReportAjaxRepository.generateLiveReportCriteria());
    const referralsWithSupportQ = referralsQ
            .then(referrals => {
                const p: Promise<TargetScheduleData[]>[] = referrals.map(r => {
                    const snapshotQ = getSupportSmartStepsSnapshotRepository().findSupportSmartStepsSnapshotByServiceRecipientIdAndEvidenceGroup(r.serviceRecipientId, EvidenceGroup.needs.name);
                    return snapshotQ.then(s => {
                        return s.latestActions
                                .filter(a => a.targetSchedule)
                                .filter(a => a.targetDateTime ?
                                        filterBefore.laterThanOrEqual(EccoDateTime.parseIso8601(a.targetDateTime)) : false
                                ).map(a => {
                                    const referralWithSnapshot: TargetScheduleData = {
                                        schedule: a,
                                        referral: r
                                    };
                                    return referralWithSnapshot;
                                })
                    });
                });
                return Promise.all(p).then(dataArr => dataArr.reduce((acc, val) => acc.concat(val), []));
            });
    return referralsWithSupportQ.then(data => ({schedules: data}));
}


/**
 * RENDERING
 */

export class TargetScheduleCardGroup extends CardGroup<TargetScheduleCardData> {
}

export class TargetScheduleCardData implements CardData {
    constructor(public dto: TargetScheduleData) {
    }

    getKey() {
        return "targetsched-" + this.dto.schedule.actionInstanceUuid
    }

    getPriority() {
        return EccoDateTime.nowLocalTime().addSeconds(this.dto.referral ? this.dto.referral.serviceRecipientId : 0);
    }
}

// used in DailyChecksPage.tsx to load
// loader loads live referrals targetSchedules due
export class TargetScheduleSource implements CardSource {

    getCards() {
        const referralsWithSupportQ = loader({date: ""}).then(data => data.schedules);

        const filterBefore = EccoDate.todayLocalTime().addDays(1);
        const dueTodaySchedule = Observable.fromPromise(referralsWithSupportQ).pipe(
                filter(resource => !!resource),
                flatMap(resource  => resource),
                map(data => new TargetScheduleCardData(data)));

        return Observable.concat<TargetScheduleCardData>(dueTodaySchedule);
        // return Observable.from([
        //     new TargetScheduleCardGroup("", Observable.concat<TargetScheduleCardData>(dueTodaySchedule),
        //             EccoDateTime.nowUtc().addDays(1)),
        // ]);
    }
}



// REDUNDANT below here, for now

const AsyncTargetScheduleData = createInstance<TargetSchedulesData>(
        {promiseFn: loader},
        "AsyncTargetScheduleData"
);


// NB this is just concerned about loading the data (not session data etc)
// although since its passing-through data, it may as well collect the withSessionData etc also, rather than use spread
// if we wanted to pass-through, just create a wrapper fn for 'with' and provide the props.children call here with {...data, ...props.otherData}
function WithTargetScheduleDataHandler(props: {initialValue?: TargetSchedulesPageData | undefined} & FulfilledProps<TargetSchedulesPageData>) {
    return withSessionData((sessionData) =>
            <Fragment>
                <AsyncTargetScheduleData.Loading>
                    <LoadingSpinner/>
                </AsyncTargetScheduleData.Loading>
                <AsyncTargetScheduleData.Resolved>
                    {
                        (data: TargetSchedulesPageData, state: AsyncFulfilled<TargetSchedulesPageData>) => {
                            //ReloadEvent.bus.addHandler(state.reload);
                            const allData = {...data, sessionData: sessionData};
                            return typeof props.children === "function" ? props.children(allData, state) : props.children;
                        }
                    }
                    {/* context => children(context.schedules) */}
                </AsyncTargetScheduleData.Resolved>
                <AsyncTargetScheduleData.Rejected>
                    {error => {
                        console.error(error);
                        return stringifyPossibleError(error);
                    }}
                </AsyncTargetScheduleData.Rejected>
            </Fragment>
    );
}

/**
 * Examples of how we can load components from many data sources, and thoughts on how to render from the data.
 *
 * LOADING: Use Approach 2
 * Approach 1 and 2 are clean, but approach 2 offers the ability to assume the loading has been done. This should be the
 * preferred approach - assuming that approach 1 triggers a load per re-use - which async may avoid through watch? (TODO quick test on 'network' tab)
 * Approach 3 seems to bypass .Loading and .Rejected etc. ErrorBoundary, from the link, just picks up on the javascript Error - not async errors.
 * It does bind the errors to just that hierarchy though, which is good.
 * For code-testing, it may be useful to provide an initialValue - see ServiceRecipientLoader.test.tsx and ServiceRecipientLoadAndRender.
 *      Approach 1: AsyncSessionData triggers the loading, and 'with' handles the .Resolved.
 *      Approach 2: ServiceRecipientLoadAndRender handles the triggering and .Resolved - which passes the data to the children.
 *      Approach 3: .Resolved on its own.
 *
 * RENDERING: Use Approach 3 or 4 (possibly 1 if the 'triggers a load per re-use' is not the case). Approach 3 is used in this file.
 * Having the render method embed the load and the component to render (as its child) is a good approach as it means we don't specify what is rendered here in the loader.
 * The difficulty is in passing the data to the 'unknown' children in the loader. What would be ideal is passing the props automagically.
 * To do this involves passing props to 'props.children'. This may be achieved:
 *      Approach 1: using a full-loader (also see ServiceRecipientLoadAndRender and FullReferralLoadAndRender), which passes all the resolved data to the children
 *              The downside here is if it triggers a load per re-use (the same possible negative for approach 1 above)
 *      Approach 2: as approach 1, but keep the loader specific to the data in question, and pass-through other data using 'with'
 *              The downside here is the pass-through needs to be one object - so the spread operator is used
 *      Approach 3: as approach 1 and 2, but split the loading from the resolving - so don't pass-through data, use the required 'with's
 *      Approach 4: not using props.children, but passing a separate function which can determine the component to draw
 *              The downside here is that its not as natural as using children, but at least the loader can be re-used
 *              To do this approach - expose the data like WithAllCareScheduleDataHandler>{data => and call props.component(data),
 *              where the prop 'component' is a method returning what the children would have been.
 *      Approach 5: using async.createInstance - simply wrap the combined data in another instance with .Resolved etc
 *      Approach 6: using React.createContext - seems more modern and supported, and is kind of what we want - a way to collect up data and pass it to children
 *      Approach 7: using React.cloneElement - seems more dated
 */
export function TargetScheduleDataLoader(props: {date?: string | undefined} & FulfilledProps<TargetSchedulesPageData>) {
    return <ErrorBoundary>
        <AsyncSessionData promiseFn={SessionDataService.getFeatures}>
            <AsyncTargetScheduleData date={props.date}>
                <WithTargetScheduleDataHandler>
                    {props.children}
                </WithTargetScheduleDataHandler>
            </AsyncTargetScheduleData>
        </AsyncSessionData>
    </ErrorBoundary>;
}



/**
 * BUILDING (shift based): For showing the tasks to do today.
 * There is cross over with CareVisit on ecco-controls because CareVisit represents the tasks to do that day for a client
 * whereas this represents the tasks to do that day for all clients (but the data is the same).
 * It may be that this simply becomes a wrapper for the CareVisitModal (eg to group a clients tasks into one card as far as possible)
 */
interface TargetScheduleProps {
    schedule: TargetScheduleData;
}


/** For Referral (should be SvcRec) + SupportAction */
export const TargetScheduleCard: FC<TargetScheduleProps> = props => {
    const eccoAPI = useServicesContext();
    const data = useMemo(() => EventBackingData.fromTargetSchedule(eccoAPI.sessionData, props.schedule), []);

    // as per EventCard - link.text == "rota visit" comes from ServiceRecipientEventDecorator
    const workUuid = data.evidenceWorkUuid ? data.evidenceWorkUuid : Uuid.randomV4().toString();
    const work = data.evidenceWorkUuid ? useWork(data.serviceRecipientId, EvidenceGroup.needs, workUuid).work : null;

    // NB additionalStaff is null because that's an appointment related concern
    const initVisitProps = transformToCareVisit(data, !!data.evidenceWorkUuid, workUuid, work, null, null, null, null, EccoDateTime.nowLocalTime().formatIso8601(), eccoAPI);
    //const initVisitProps = useMemo(() => transformReferralAndActionsToCareVisit(props.schedule.referral, [props.schedule.schedule]), []);
    return (
        <CareVisitRoot careVisitInitState={initVisitProps}>
            <CareVisitSummaryCard/>
        </CareVisitRoot>
    );
}