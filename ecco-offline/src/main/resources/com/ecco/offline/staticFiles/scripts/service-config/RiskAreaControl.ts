import {Uuid} from "@eccosolutions/ecco-crypto";
import {apiClient} from "ecco-components";
import {ActionGroupDto, RiskAreaDto} from "ecco-dto";
import {ActionDefChangeCommand, ActionGroupDefChangeCommand} from "./commands";
import {EntryRow, EntryRowNameOrderDisabled, ListEntryDto, ListOrderedControl} from "./ListOrderedControl";
import {OutcomeAjaxRepository} from "./OutcomeAjaxRepository";

const repository = new OutcomeAjaxRepository(apiClient);

class EntryRowParent extends EntryRowNameOrderDisabled {

    constructor(data: ListEntryDto) {
        super(data, data.id
            ? new ActionGroupDefChangeCommand("update", data.parentDefUuid, data.uuid)
            : new ActionGroupDefChangeCommand("add", data.parentDefUuid, data.uuid)
        );
    }

    public getEntryRowsChildren(): EntryRow[] {
        return this.listEntryDto.children.map(c => {
            return new EntryRowChild(c);
        });
    }

    createEntryRowChildText(): string {
        return "add new action";
    }

    public createEntryRowChild(orderBy: number) {
        const dto: ListEntryDto = {
            id: null,
            uuid: Uuid.randomV4().toString(),
            parentDefUuid: this.listEntryDto.uuid.toString(),
            name: "",
            orderby: orderBy,
            disabled: false,
            children: []
        };
        return new EntryRowChild(dto);
    }

}

class EntryRowChild extends EntryRowNameOrderDisabled {

    constructor(data: ListEntryDto) {
        super(data, data.id
            ? new ActionDefChangeCommand("update", data.uuid, data.parentDefUuid)
            : new ActionDefChangeCommand("add", data.uuid, data.parentDefUuid, data.orderby));
    }

    public createEntryRowChildText(): string {
        return null;
    }

    public getEntryRowsChildren(): EntryRow[] {
        return null;
    }

    public createEntryRowChild(orderBy: number) {
        return null;
    }
}

class RiskAreaDefControl extends ListOrderedControl<RiskAreaDto> {

    private riskArea: RiskAreaDto;

    constructor(private riskAreaId: number) {
        super();
    }

    public getManageEntityText(): string {
        return "actions";
    }

    public getNewParentEntryRowText(): string {
        return "add new group";
    }

    protected fetchViewData(): Promise<RiskAreaDto> {
        return repository.findOneRiskArea(this.riskAreaId).then(o => {
            this.riskArea = o;
            return o;
        });
    }

    public getParentEntryRows(data: RiskAreaDto)  {
        const rows = data.actionGroups.map((ag: ActionGroupDto) => {
            const conv: ListEntryDto = {
                id: ag.id,
                uuid: ag.uuid,
                name: ag.name,
                parentDefUuid: data.uuid,
                orderby: ag.orderby,
                disabled: ag.disabled,
                children: ag.actions.map(a => {
                    const aConv: ListEntryDto = {
                        id: a.id,
                        uuid: a.uuid,
                        name: a.name,
                        parentDefUuid: ag.uuid,
                        orderby: a.orderby,
                        disabled: a.disabled,
                        children: []
                    };
                    return aConv;
                })
            };
            return new EntryRowParent(conv);
        });
        return rows;
    }

    public getNewParentEntryRow(data: RiskAreaDto): EntryRow {
        const dto: ListEntryDto = {
            id: null,
            uuid: Uuid.randomV4().toString(),
            parentDefUuid: this.riskArea.uuid.toString(),
            name: "",
            orderby: null,
            disabled: false,
            children: []
        };
        return new EntryRowParent(dto);
    }

}

export default RiskAreaDefControl;
