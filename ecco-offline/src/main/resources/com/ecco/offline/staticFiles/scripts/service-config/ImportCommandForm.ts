import $ = require("jquery");

import BaseAsyncCommandForm from "../cmd-queue/BaseAsyncCommandForm";
import Form from "../controls/Form";
import InputGroup from "../controls/InputGroup";
import { MergeableCommand } from "ecco-commands";
import {showFormInModalDom} from "../components/MUIConverterUtils";
import TextAreaInput from "../controls/TextAreaInput";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {CommandDto} from "ecco-dto";

/**
 * Import service type commands
 */
class ImportCommandForm extends BaseAsyncCommandForm<string> {

    public static showInModal(serviceTypeId: number) {
        var form = new ImportCommandForm(serviceTypeId);
        form.load();
        showFormInModalDom(form);
    }

    private form = new Form();
    private commandsTxt = new TextAreaInput("commandsId", 20, undefined, "form-control");

    /**
     * @param serviceTypeId to replace the one provided, also changes the commandUri
     */
    constructor(private serviceTypeId: number) {
        super("commands");
        this.commandsTxt.element()
                .css('margin-left', 'auto')
                .css('margin-right', 'auto');
        this.form
            .append( new InputGroup("commands", this.commandsTxt) );
    }

    protected fetchViewData(): Promise<string> {
        return Promise.resolve(null);
    }

    protected render() {
        this.enableSubmit(); // probably want to have this be linked to length of commandQueue (based on event?)
        this.element().empty();
        this.append(this.form);
    }

    protected override submitForm(): Promise<void> {
        var cmdsArrayAsText = this.commandsTxt.val();

        // coerce back into commands
        var jsonData = $.parseJSON(cmdsArrayAsText);
        for (var i = 0; i < jsonData.length; i++) {
            var cmdJson = jsonData[i];
            var cmd = new CoercedCommand(cmdJson, this.serviceTypeId);
            this.commandQueue.addCommand(cmd);
        }
        return super.submitForm();
    }

}

/**
 * We could cast to the specific command, eg ServiceConfigTaskEntryChangeCommand,
 * but we don't know what the commands could be. We could also simply cast to a common
 * base MergeableCommand, but this is effectively the same thing in this class
 */
class CoercedCommand implements MergeableCommand {

    private cmdJson: any;

    constructor(cmdJsonIn: any, private serviceTypeId: number) {
        this.replaceCommandUuid(cmdJsonIn);
        this.replaceCommandUri(cmdJsonIn);
        this.replaceBody(cmdJsonIn);
        this.cmdJson = cmdJsonIn;
    }

    getUuid(): Uuid {
        return this.cmdJson.uuid;
    }

    getCommandTargetUri(): string {
        return this.cmdJson.commandUri;
    }

    canMerge(candidateToMerge: this): boolean {
        return false;
    }

    merge(previousCommand: this): this {
        throw new Error("merge() not implemented.");
    }

    toDto(): CommandDto {
        return this.cmdJson;
    }

    /**
     * The 'copy'/export do create anew uuid for the command, but we can't apply this more than once.
     * So on the 'import' screen we set it a new uuid.
     * @param cmdJsonIn
     */
    private replaceCommandUuid(cmdJsonIn: CommandDto) {
        // var uriReg = /"uuid":\s*"(.*)"/;
        // var uuid: string = (<string>cmdJsonIn.uuid).replace(uriReg, 'service-config/' + this.serviceTypeId.toString() + '/');
        cmdJsonIn.uuid = Uuid.randomV4().toString();
    }

    private replaceCommandUri(cmdJsonIn: any) {
        var uriReg = /service-config\/(-?)\d+\//;
        var uri: string = (<string>cmdJsonIn.commandUri).replace(uriReg, 'service-config/' + this.serviceTypeId.toString() + '/');
        cmdJsonIn.commandUri = uri;
    }

    private replaceBody(cmdJsonIn: any) {
        if (cmdJsonIn.serviceTypeId) {
            cmdJsonIn.serviceTypeId = this.serviceTypeId.toString();
        }
    }

}

export default ImportCommandForm;
