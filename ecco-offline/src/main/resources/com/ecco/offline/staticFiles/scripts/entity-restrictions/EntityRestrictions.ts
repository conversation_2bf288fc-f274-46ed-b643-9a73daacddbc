import $ = require("jquery")

import SelectList from "../controls/SelectList";
import {EntityRestrictionsRepository} from "./EntityRestrictionsRepository"
import {ProjectDto, ServiceDto, SessionData, SessionDataRepository} from "ecco-dto";

class EntityRestrictions {

    private $container: $.JQuery;

    private sessionData: SessionData;
    private servicesProjects: ServiceDto[];
    private servicesSelect: SelectList;
    private projectsSelect: SelectList;
    private initialServiceValue: string;
    private initialProjectValue: string;

    constructor(private repository: EntityRestrictionsRepository, private sessionDataRepo: SessionDataRepository,
                private onChange: (source: EntityRestrictions) => void, private emptyEntryText = "-") {
    }

    // TODO we need to make this part of the constructor - in place of the repository
    // and change it with a promise, regardless of the source
    public setServicesProjects(servicesDto: ServiceDto[]) {
        this.servicesProjects = servicesDto;
    }

    /** Attaches and creates the EntityRestrictions jQuery elements on $inputElement. */
    public attachTo($inputElement: $.JQuery, nameForServiceSelect?: string, nameForProjectSelect?: string): void {
        this.$container = $inputElement;
        // get any initial values from generated pages (before we clear the container)
        this.initialServiceValue = this.$container.attr("data-initial-value-service");
        this.initialProjectValue = this.$container.attr("data-initial-value-project");

        // TODO currently only services/projects are populated for restrictions in the drop down, but this can be extended as needed (eg contracts)
        var ns = nameForServiceSelect ? nameForServiceSelect : "services-select";
        var np = nameForProjectSelect ? nameForProjectSelect : "projects-select";
        this.createRestrictedServicesProjects(ns, np);
        this.loadRestrictedServicesProjects();
    }

    public getSelectedService(): number {
        var service = this.servicesSelect.selected(false).toString();
        return service ? parseInt(this.servicesSelect.selected(false).toString()) : null;
    }
    public getSelectedProject(): number {
        return this.projectsSelect.selected(false).toString();
    }
    /*
    public setServiceId(serviceId: number) {
        this.servicesSelect.setSelected(serviceId.toString());
    }
    public setProjectId(projectId: number) {
        this.projectsSelect.setSelected(projectId.toString());
    }
    */

    /* Creates the drop down lists for services/projects */
    private loadRestrictedServicesProjects() {

        var onSuccess = (services: ServiceDto[]) => {
            // transfer the query result to this object
            this.servicesProjects = services;
            this.servicesSelect.populateFromList(this.servicesProjects,
                (service) => ({ key: service.id.toString(), value: service.name}),
                (item) => item.id.toString() == this.initialServiceValue);
            if (this.initialServiceValue) {
                this.onChangeService(this.initialServiceValue);
            }
        };

        if (this.servicesProjects) {
            onSuccess(this.servicesProjects);
            return;
        }

        const sessionDataQ = this.sessionDataRepo.getSessionData();
        var servicesProjectsQ = this.repository.findRestrictedServicesProjects();
        sessionDataQ.then(sessionData => {
            servicesProjectsQ.then(sp => {
                this.sessionData = sessionData;
                onSuccess(sp)
            })
        })
    };

    /* Creates the drop down lists for services/projects */
    private createRestrictedServicesProjects(nameForServiceSelect: string, nameForProjectSelect: string) {

        this.$container.empty();

        // create the drop downs
        this.createRestrictedServices(nameForServiceSelect);
        this.createRestrictedProjects(nameForProjectSelect);

        var $ddlSrv = this.servicesSelect.element();
        var $ddlPrj = this.projectsSelect.element();
        var $rowSrv = $("<div>").attr("class", "e-row");
        $("<span>").attr("class", "e-label").text("service ").appendTo($rowSrv);
        $("<span>").attr("class", "input").append($ddlSrv).appendTo($rowSrv);
        var $rowPrj = $("<div>").attr("class", "e-row");
        $("<span>").attr("class", "e-label").text("project ").appendTo($rowPrj);
        $("<span>").attr("class", "input").append($ddlPrj).appendTo($rowPrj);

        this.$container.append($rowSrv).append($rowPrj);
    }

    /**
     * Private helper methods to create the lists and handle choosing service/project combinations
     */
    private createRestrictedServices(nameForServicesSelect: string): void {
        // create the services from the data received
        this.servicesSelect = new SelectList(nameForServicesSelect, true, this.emptyEntryText);
        this.servicesSelect.change((id) => this.onChangeService(id));
    }

    private createRestrictedProjects(nameForProjectSelect: string): void {
        // start with an empty projects list
        this.projectsSelect = new SelectList(nameForProjectSelect, true, this.emptyEntryText);
        this.projectsSelect.change((id) => this.onChangeProject(id));
    }

    /**
     * Changing a service updates the project drop down and indicates an EntityRestriction.onChange callback
     */
    private onChangeService(id: string) {

        // clear the project list contents
        this.projectsSelect.clear();

        // build an array of what can be seen
        // REVIEW - this could be dom-rich by creating a dom for the dto?
        var projects: ProjectDto[] = null;
        for (var i = 0; i < this.servicesProjects.length; ++i) {
            var service = this.servicesProjects[i];
            // compare strings as javascript can't compare longs exactly, only integers - http://stackoverflow.com/questions/17320706/javascript-long-integer
            if (""+service.id == id) {
                projects = this.sessionData.getServiceCategorisationProjects(service.id, true);
            }
        }

        // assign what can be seen
        this.projectsSelect.populateFromList(projects,
            (project) => ({ key: project.id.toString(), value:project.name}),
            (project) => project.id.toString() == this.initialProjectValue);

        this.onChange(this);
    }

    /**
     * Changing a project simply calls EntityRestriction.onChange callback
     */
    private onChangeProject(id: string) {
        this.onChange(this);
    }

}

export default EntityRestrictions;
