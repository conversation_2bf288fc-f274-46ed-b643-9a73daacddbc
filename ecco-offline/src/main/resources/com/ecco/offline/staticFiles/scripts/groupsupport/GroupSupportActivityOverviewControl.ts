import GroupActivityOverviewControl from "./GroupActivityOverviewControl";
import {GroupActivityDto, SessionData} from "ecco-dto";
import {GroupSupportActivityClientListControl} from "./GroupSupportActivityClientListControl";
import {groupSupportActivityOptions} from "./GroupSupportActivityList";

class GroupSupportActivityOverviewControl extends GroupActivityOverviewControl {

    private supportOptions = groupSupportActivityOptions;

    protected options() {
        return this.supportOptions;
    }

    protected label(lookup: string) {
        switch (lookup) {
            case "attending":
                return "attending";
            case "attended":
                return "attended";
            case "attendance":
                return "attendance";
            case "register":
                return "printable invited register";
            default:
                return lookup;
        }
    }

    protected createClientListControl(sessionData: SessionData, activity: GroupActivityDto) {
        return new GroupSupportActivityClientListControl(sessionData, activity, this.options());
    }

}
export default GroupSupportActivityOverviewControl;