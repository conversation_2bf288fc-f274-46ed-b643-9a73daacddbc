import $ = require("jquery");
import GroupActivityListControl, {GroupRowControl} from "./GroupActivityListControl";
import {ClientAttendanceDto, GroupActivityDto, ResourceList, SessionData} from "ecco-dto";
import {apiClient} from "ecco-components";
import {ClientAttendance, GroupActivity, GroupSupportAjaxRepository} from "ecco-dto";
import {GroupActivityCommand, GroupActivityInvitationCommand} from "./commands";
import {Uuid} from "@eccosolutions/ecco-crypto";
import moment = require("moment");
import {EccoDate, StringToObjectMap} from "@eccosolutions/ecco-common";
import {GroupPageType} from "ecco-dto";

const repository = new GroupSupportAjaxRepository(apiClient);

export class GroupSupportActivityListControl extends GroupActivityListControl {

    protected findActivities(page: number): Promise<ResourceList<GroupActivityDto>> {
        return this.parentId
            ? repository.findSupportActivitiesByParent(page, this.parentId)
            : repository.findSupportActivities(page, this.groupPageType, this.serviceId, this.svcCatId, this.activityTypeId, this.venueId)
    }

    protected createRowControl(activity: GroupActivityDto): GroupRowControl {
        return new SupportRowControl(this.groupPageType, activity, this.sessionData, () => this.update(this.groupPageType, this.activityTypeId, this.venueId, this.serviceId, this.svcCatId, this.page));
    }

    protected override getMenu() {
        return null;
    }

    protected getHeaders() { // OVERRIDES default impl
        const headings = [];
        if (this.groupPageType == "sessions") {
            headings.push("date");
        }
        headings.push("activity");
        headings.push("description");
        headings.push("service");
        headings.push("project");
        headings.push("venue");
        if (this.groupPageType == "sessions") {
            headings.push("invited");
            headings.push("attending");
            // if we are showing the group, don't bother with the link to the group
            if (!this.parentId) {
                headings.push("course");
            }
        } else {
            headings.push("start");
            headings.push("end");
            headings.push("sessions");
        }
        headings.push("-");
        return headings;
    }
}

class SupportRowControl extends GroupRowControl {

    constructor(groupPageType: GroupPageType, activity: GroupActivityDto, sessionData: SessionData, reload: () => void) {
        super(groupPageType, activity, sessionData, reload);
    }

    protected findClientsByActivityId(activityId: number): Promise<ClientAttendanceDto[]> {
        return repository.findInvitedClientsByActivityId(activityId);
    }
    protected findOneActivityByUuid(activityUuid: string): Promise<GroupActivity> {
        return repository.findOneActivityByUuid(activityUuid);
    }

    protected copyActivityCmd(activityNewUuid: Uuid): GroupActivityCommand {
        return new GroupActivityCommand("create", activityNewUuid, GroupActivityCommand.DISCRIMINATOR_SUPPORT, this.activity.parentId);
    }

    protected copyActivityDetailCmds(activityNew: GroupActivity, attendees: ClientAttendance[]) {
        return attendees.map(a => {
            return new GroupActivityInvitationCommand("update",
                    activityNew.getDto().id, Uuid.parse(activityNew.getDto().uuid), a.getDto().referralSummary.referralId)
                    .changeInvited(null, a.getDto().invited)
                    .changeAttending(null, a.getDto().attending);
            //cmd.changeAttended(rowData.clientAttendance.attended, rowData.attended);
            //cmd.changeEvidenceNotes(rowData.clientAttendance.comment, rowData.evidenceNotes);
            //cmd.changeEvidenceType(rowData.clientAttendance.typeId, rowData.typeId);
        });
    }

    private startDate(item, link: boolean) {
        const label = moment(item.startDateTime).format("DD MMMM YYYY HH:mm");
        if (!link) {
            return label;
        }
        return this.link(item.id, label);
    }

    private link(itemId: number, label: string) {
        // we can now be in a group activity list so the url is already group-support/150
        // so extract to 'group-support' and re-apply the id
        const url = `${window.location.pathname}`;
        const urlPrefix = url.substring(0, url.indexOf("group-support") + 13);
        return $("<a>").attr("href", urlPrefix + "/" + itemId + "/").text(label);
    }

    protected getColumnMapping(): StringToObjectMap<(dto: GroupActivityDto) => string|$.JQuery> {

        return {
            "activity": item => {
                return item.course
                    ? this.link(item.id, this.sessionData.getListDefinitionEntryById(item.activityTypeId)?.getDisplayName())
                    : this.sessionData.getListDefinitionEntryById(item.activityTypeId)?.getDisplayName()
            },
            "description": item => item.description,
            "service": item => item.serviceId ? this.sessionData.getServiceName(item.serviceId) : "-",
            "project": item => item.projectId ? this.sessionData.getProjectName(item.projectId) : "-",
            "venue": item => item.venueName,
            "invited": item => item.clientsInvited ? item.clientsInvited.toString() : "0",
            "attending": item => item.clientsAttending ? item.clientsAttending.toString() : "0",
            "start": item => {
                return this.startDate(item, false);
            },
            "date": item => {
                return this.startDate(item, true);
            },
            "end": item => EccoDate.iso8601ToFormatShort(item.endDate),
            "sessions": item => item.childrenCount ? item.childrenCount.toString() : "",
            "course": item => {
                return item.parentId
                    ? $("<a>").attr("href", item.parentId + "/").text("open")
                    : null;
            },
            "-": item => this.adminButtonsFor(item)
        };
    }

}
