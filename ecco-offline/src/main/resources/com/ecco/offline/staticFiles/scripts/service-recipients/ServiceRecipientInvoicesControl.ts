import $ = require("jquery");
import ActionButton from "../controls/ActionButton";
import BaseTableRowControl from "../controls/BaseTableRowControl";
import PagedAsyncTableControl from "../controls/PagedAsyncTableControl";
import ServiceRecipientEditInvoiceForm from "./ServiceRecipientEditInvoiceForm";
import {EccoDate, StringToObjectMap} from "@eccosolutions/ecco-common";
import {apiClient} from "ecco-components";
import {InvoiceDto, InvoicesAjaxRepository, InvoiceStatus} from "ecco-dto";
import {onParentTabActivated} from "../common/tabEvents";

const repository = new InvoicesAjaxRepository(apiClient);

/*
function findHref(links: LinkDto[], rel: string) {
    if (!links)
        return null;
    let matches = links.filter( link => link.rel == rel );
    return matches.length == 0 ? null : matches[0].href;
}
*/

class RowControl extends BaseTableRowControl<InvoiceDto> { // TODO would be InvoicesListRow

    constructor(private invoice: InvoiceDto, private editable: boolean) {
        super(invoice);
    }

    protected override getColumnMapping(): StringToObjectMap<(dto: InvoiceDto) => string|$.JQuery> {
        return {
            "i-id":     item => {
                let btn = new ActionButton(item.invoiceId.toString(), undefined, true);
                btn.clickSynchronous(() =>
                    ServiceRecipientEditInvoiceForm.showInModal(item.invoiceId)
                );
                if (!this.editable) {
                    btn.disable();
                }
                return btn.element();
                //let link = findHref(item.links, "edit");
                //     .attr("href", link)
                //     //.attr("title", "")
                //     .text(item.invoiceDat);
            },
            "date":     item => EccoDate.iso8601ToFormatShort(item.invoiceDate),
            "status":   item => InvoiceStatus[InvoiceStatus[item.status]], // see http://stackoverflow.com/a/36743651
            "amount":   item => item.amount.toString()
        };
    }

}


/** Renders a simple list of the nearby events that are in Referral.calendarEvents */
class ServiceRecipientInvoicesControl extends PagedAsyncTableControl<InvoiceDto> {

    constructor(private serviceRecipientId: number, private editable: boolean) {
        super("invoices", "invoices-list container-fluid top-gap-15 bottom-gap-15");
    }

    protected override afterAttach() {
        onParentTabActivated(this.element(), () => {
            this.load();
        })
    }

    protected fetchViewData(): Promise<InvoiceDto[]> {
        return repository.findAllInvoicesByServiceRecipientId(this.serviceRecipientId);
            // TODO pagination arguments... this.page -1, this.pageSize (see shelved code)
            // .then ( resourceList => {
            //     this.hasNext = resourceList.links.some( link => link.rel == "next" );
            //     this.numPages = resourceList.numPages;
            //     return resourceList.data;
            // }); // Not ideal, but sufficient for now
    }

    protected getHeaders() { // OVERRIDES default impl
        return ["i-id", "date", "status", "amount"];
    }

    protected createRowControl(invoice: InvoiceDto) {
        return new RowControl(invoice, this.editable);
    }

    protected getMenu() {
        return $("<div>")
            .addClass("text-center")
            .append($("<h4>invoices</h4>"));
    }

}
export default ServiceRecipientInvoicesControl;
