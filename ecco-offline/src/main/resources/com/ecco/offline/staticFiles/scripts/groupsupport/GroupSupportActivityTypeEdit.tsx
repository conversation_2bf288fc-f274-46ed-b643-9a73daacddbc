import {CommandQueue, CommandSource} from "ecco-commands";
import {
    apiClient,
    AsyncSessionData,
    CommandForm,
    CommandSubform,
    LoadingSpinner,
    withCommandForm
} from "ecco-components";
import {textInput, possiblyModalForm, checkBox} from "ecco-components-core";
import {ActivityTypeDto, SessionData} from "ecco-dto";
import {stringifyPossibleError, withAuthErrorHandler} from "ecco-offline-data";

import * as React from "react";
import {ClassAttributes, Fragment, ReactNode} from "react";
import {createInstance} from "react-async";
import {showInCommandForm} from "../components/CommandForm";
import {Grid } from '@eccosolutions/ecco-mui';
import {GroupSupportAjaxRepository} from "ecco-dto";
import {GroupSupportTypeCommand} from "./commands";

const repository: GroupSupportAjaxRepository = new GroupSupportAjaxRepository(apiClient.withCachePeriod(0));

/**
 * LOAD DATA (copied from CalendarEntryForm)
 */
// The react-async 'createInstance' approach to loading data.
// This is very repetitive for each bit of data!
// NB Context is used as State too
interface GroupSupportTypeContext {
    entry: ActivityTypeDto;
}
function loader(props: {entryId: number}): Promise<GroupSupportTypeContext> {
    // ErrorBoundary is done in loader, showInCommandForm and possiblyModel
    return withAuthErrorHandler(
        repository.findActivityTypes().then(data => {
            return {entry: data.filter(t => t.id == props.entryId).pop()};
        })
    );
}
const AsyncGroupSupportTypeEntryData = createInstance<GroupSupportTypeContext>(
    {promiseFn: loader},
    "AsyncGroupSupportTypeData"
);

// NB this is just concerned about loading the data for its children (not session data etc)
function withGroupSupportTypeEntry(entryId: number, children: (value: ActivityTypeDto) => ReactNode) {
    return <Fragment>
        <AsyncGroupSupportTypeEntryData entryId={entryId}>
            <AsyncGroupSupportTypeEntryData.Loading>
                <LoadingSpinner/>
            </AsyncGroupSupportTypeEntryData.Loading>
            <AsyncGroupSupportTypeEntryData.Resolved>
                {context => children(context.entry)}
            </AsyncGroupSupportTypeEntryData.Resolved>
            <AsyncGroupSupportTypeEntryData.Rejected>
                {error => {
                    console.error(error);
                    return stringifyPossibleError(error);
                }}
            </AsyncGroupSupportTypeEntryData.Rejected>
        </AsyncGroupSupportTypeEntryData>
    </Fragment>;
}
/**
 * LOAD FORM - non-jsx modal
 * This top level function instigates the commandform (in showInCommandForm), and data
 */
export function groupSupportTypeEntry(entryId?: number | undefined) {
    showInCommandForm(
        <GroupSupportTypeEntryEditor entryId={entryId}/>
    );
}
/**
 * LOAD FORM - jsx
 * This top level function assumes the caller has loaded the required data - otherwise you'll get nothing.
 */
const GroupSupportTypeEntryEditor = (props: {entryId?: number | undefined, formRef?: ((e: GroupSupportActivityTypeEdit) => void) | undefined}) =>
    withCommandForm(commandForm =>
        possiblyModalForm(
            "list definition entry",
            true, true,
            () => commandForm.cancelForm(),
            () => commandForm.submitForm(),
            false, // TODO could emitChangesTo and see if there are any commands
            false,
            props.entryId
                ? getEditGroupSupportTypeEntrySubform(props.entryId, props.formRef, commandForm)
                : getNewGroupSupportTypeSubform(props.formRef, commandForm)
        )
    );

/**
 * This is concerned about rendering the form - with all the data is required
 * formRef must extend CommandSource.  Used to emitChangesTo(cmdQ) when submitting. Could also ask hasChanges()...
 */
function getNewGroupSupportTypeSubform(formRef: (c: GroupSupportActivityTypeEdit) => void,
                                    commandForm?: CommandForm | undefined) {
    // ErrorBoundary is done in loader, showInCommandForm and possiblyModel
    return <AsyncSessionData.Resolved>{sessionData =>
            <GroupSupportActivityTypeEdit
                ref={formRef}
                entry={null}
                readOnly={false}
                sessionData={sessionData}
                commandForm={commandForm}
            />
        }
        </AsyncSessionData.Resolved>
    ;
}
function getEditGroupSupportTypeEntrySubform(entryId: number,
                                     formRef: (c: GroupSupportActivityTypeEdit) => void,
                                     commandForm?: CommandForm | undefined) {
    // ErrorBoundary is done in loader, showInCommandForm and possiblyModel
    return <AsyncSessionData.Resolved>{sessionData =>
            withGroupSupportTypeEntry(entryId, entry =>
                <GroupSupportActivityTypeEdit
                    ref={formRef}
                    entry={entry}
                    readOnly={false}
                    sessionData={sessionData}
                    commandForm={commandForm}
                />
            )}
        </AsyncSessionData.Resolved>
    ;
}

interface Props extends ClassAttributes<GroupSupportActivityTypeEdit> {
    readOnly: boolean;
    entry: ActivityTypeDto;
    sessionData: SessionData;
}

interface State {
    id: number;
    name: string;
    disabled: boolean;
}

function isEmpty(str: any) {
    return !str || !str.length;
}

export class GroupSupportActivityTypeEdit extends CommandSubform<Props, State> implements CommandSource {

    public static showInModal(id: number) {
        groupSupportTypeEntry(id);
    }

    constructor(props) {
        super(props);

        const e = this.props.entry;
        this.state = {
            id: e && e.id,
            name: e && e.name,
            disabled: e && e.disabled
        }
    }

    emitChangesTo(commandQueue: CommandQueue) {
        let cmd: GroupSupportTypeCommand;

        if (this.props.entry) {
            cmd = new GroupSupportTypeCommand("update", this.props.entry.id);
        }
        else {
            cmd = new GroupSupportTypeCommand("add", null);
        }

        const e = this.props.entry;
        cmd.changeName(e && e.name, this.state.name);
        cmd.changeDisabled(e && e.disabled, this.state.disabled);

        if (cmd.hasChanges()) {
            commandQueue.addCommand(cmd);
        }
    }

    getErrors(): string[] {
        type DataField = keyof State; //[P in keyof ListDefinitionEntryField]
        const requiredFields: DataField[] = ['name'];
        return requiredFields.reduce( (errors, requiredField) => {
            if (isEmpty(this.state[requiredField])) {
                errors.push(`${requiredField} is required`);
            }
            return errors;
        }, []);
    }

    render() {
        const setter = state => this.setState(state);
        return (
            <Grid container>
                <Grid item xs={12}>
                    {textInput("name", "name", setter, this.state, undefined, this.props.readOnly, true)}
                </Grid>
                <Grid item xs={2}>
                    {checkBox("disabled", "disabled", setter, this.state, this.props.readOnly)}
                </Grid>
            </Grid>
        );
    }
}
export default GroupSupportActivityTypeEdit;
