import $ = require("jquery");
import _ = require("lodash");

import BaseAsyncDataControl from "../../controls/BaseAsyncDataControl";
import {apiClient, getGlobalEccoAPI} from "ecco-components";
import {ProjectDto, ServiceDto} from "ecco-dto";
import {EntityRestrictionsAjaxRepository} from "../../entity-restrictions/EntityRestrictionsAjaxRepository";

var entityRestrictionsRepository = new EntityRestrictionsAjaxRepository(apiClient);

class ServiceProjectSelectorControl extends BaseAsyncDataControl<ServiceDto[]> {

    private serviceIndex: Record<string, ServiceDto>;

    private serviceId: number;

    private projectId: number;

    private $serviceSelect: $.JQuery;

    private $projectSelect: $.JQuery;

    constructor(private onServiceSelected?: (serviceId: number) => void) {
        super();

        this.$serviceSelect = $("<select>");
        this.$projectSelect = $("<select>");

        this.$serviceSelect.on("change", this.onServiceChanged.bind(this));
    }

    protected fetchViewData(): Promise<ServiceDto[]> {
        return entityRestrictionsRepository.findRestrictedServicesProjects();
    }

    protected render(services: ServiceDto[]) {
        services = services.sort( (a,b) => a.name.localeCompare(b.name) );

        this.serviceIndex = _.keyBy(services, "id");

        this.$serviceSelect.empty();
        this.$serviceSelect.append('<option value=""></option>');

        if (services) {
            services.forEach(service => {
                var $option = $("<option>");
                $option.attr("value", service.id);
                $option.append(service.name);
                this.$serviceSelect.append($option);
            });
        }

        this.$serviceSelect.val(this.serviceId);
        const svcCat = getGlobalEccoAPI().sessionData.getServiceCategorisationByIds(this.serviceId, this.projectId);
        const projects = getGlobalEccoAPI().sessionData.getServiceCategorisationProjects(this.serviceId, svcCat?.id || true);
        this.renderProjectOptions(projects);

        this.element().empty();
        //this.$serviceSelect.trigger("change");
        // do not append selects to element() since they will be appended to other parents
    }

    private renderProjectOptions(projects: ProjectDto[]) {
        this.$projectSelect.empty();
        this.$projectSelect.append('<option value=""></option>');

        if (projects) {
            projects = projects.sort( (a,b) => a.name.localeCompare(b.name) );

            projects.forEach(project => {
                var $option = $("<option>");
                $option.attr("value", project.id);
                $option.append(project.name);
                this.$projectSelect.append($option);
            });
        }

        this.$projectSelect.val(this.projectId);
        this.$projectSelect.trigger("change");
    }

    private onServiceChanged(event: $.BaseJQueryEventObject) {
        var service = this.serviceIndex[this.$serviceSelect.val()];
        const svcCat = getGlobalEccoAPI().sessionData.getServiceCategorisationByIds(service?.id, this.projectId);
        const projects = getGlobalEccoAPI().sessionData.getServiceCategorisationProjects(service?.id, svcCat?.id || true);
        this.renderProjectOptions(projects);

        if (this.onServiceSelected) {
            this.onServiceSelected(this.$serviceSelect.val());
        }
    }

    public getService(): number {
        return this.serviceId = this.$serviceSelect.val() && parseInt(this.$serviceSelect.val()) || null;
    }

    public setService(serviceId: number) {
        this.$serviceSelect.val(this.serviceId = serviceId);
    }

    public getProject(): number {
        return this.projectId = this.$projectSelect.val() && parseInt(this.$projectSelect.val()) || null;
    }

    public setProject(projectId: number) {
        this.$projectSelect.val(this.projectId = projectId);
    }

    public hasProjects(): boolean {
        var service = this.serviceIndex[this.$serviceSelect.val()];
        const projects = getGlobalEccoAPI().sessionData.getServiceCategorisationProjects(service?.id, true);
        return projects?.length > 0;
    }

    public selectServiceElement(): $.JQuery {
        return this.$serviceSelect;
    }

    public selectProjectElement(): $.JQuery {
        return this.$projectSelect;
    }

}
export default ServiceProjectSelectorControl;
