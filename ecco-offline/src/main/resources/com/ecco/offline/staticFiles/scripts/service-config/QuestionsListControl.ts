import $ = require("jquery");

import BaseAsyncListControl from "../controls/BaseAsyncListControl";
import BaseListEntryControl from "../controls/BaseListEntryControl";
import EditQuestionForm from "./EditQuestionForm";
import {apiClient} from "ecco-components";
import {Question, QuestionGroupAjaxRepository} from "ecco-dto";

var repository = new QuestionGroupAjaxRepository(apiClient);


class EntryControl extends BaseListEntryControl<Question> {

    constructor(question: Question) {
        super(question, "fa fa-pencil");
    }

    protected administerEntry(): void {
        EditQuestionForm.showInModal(this.entry.id);
    }

    protected getEditElement(): $.JQuery {
        return $("<span>").text(this.entry.name);
    }

    protected getEntryIconClass(): string {
        return "fa fa-question";
    }
}

class QuestionsListControl extends BaseAsyncListControl<Question> {

    constructor() {
        super("add new question", "no questions defined", "fa fa-cogs");
    }

    protected fetchViewData(): Promise<Question[]> {
        return repository.findAllQuestions();
    }
    protected createItemControl(question: Question) {
        return new EntryControl(question);
    }

    protected addNewEntity() {
        EditQuestionForm.showInModal(null);
    };

}
export default QuestionsListControl;
