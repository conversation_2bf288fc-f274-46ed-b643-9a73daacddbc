import $ = require("jquery");
import View from "../controls/View";
import Modal from "../controls/Modal";
import Form from "../controls/Form";
import ActionButton from "../controls/ActionButton";
import ElementContainer from "../controls/ElementContainer";
import HtmlElement from "../controls/HtmlElement";

import DateInput from "../controls/DateInput";
import InputGroup from "../controls/InputGroup";
import HorizontalInputGroup from "../controls/HorizontalInputGroup";
import {EccoDate} from "@eccosolutions/ecco-common";
import {ValidationCheck, ValidationChecksBuilder, ValidationErrors} from "../common/validation";
import {CommandQueue, CreateInvoiceCommand} from "ecco-commands";
import {getCommandQueueRepository} from "ecco-offline-data";

class ServiceRecipientCreateInvoiceForm implements View {

    public static showInModalByIds(serviceRecipientId: number) {
        let modal = new Modal("modal-lg");
        let submitted = () => {
            modal.dialogHide();
            // TODO auto-open ServiceRecipientEditInvoiceForm
        };
        const form = new ServiceRecipientCreateInvoiceForm(serviceRecipientId, "create invoice", submitted);
        modal.popView(form);
    }

    private controlContainer = new ElementContainer("invoice-setup-control")
        .append(new ElementContainer().addClass("row"));
    private form = new Form();
    private footer = new HtmlElement($("<span>"));
    private nextInvoiceDateInput: DateInput;

    constructor(private serviceRecipientId: number,
                private _title: string, private submitted: () => void) {
        this.controlContainer.addClass("col-md-8 col-xs-12");
        this.layoutNewInvoiceForm();
        this.populateForm();
    }

    // as per ReviewChoicesControl - could be shared code, or just wait for hateos
    private layoutNewInvoiceForm() {
        let createInvoiceBtn = new ActionButton("create").addClass("btn btn-primary");

        //let date = this.reviewChoices.dto.nextReviewDate != null ? EccoDate.parseIso8601(this.reviewChoices.dto.nextReviewDate) : null;
        let date = null;
        this.nextInvoiceDateInput = new DateInput(date, "invoiceDate", true);
        let nextInvoiceDateValidate = new InputGroup("invoice date", this.nextInvoiceDateInput)
            .withValidationChecks(new ValidationChecksBuilder()
                    .addCheck(ValidationCheck.Required)
                , new ValidationErrors(""))
            .withCallback((valid: boolean) => {
                valid ? createInvoiceBtn.enable() : createInvoiceBtn.disable();
            })
            .enableValidation();
        let nextReviewDateGroup = new HorizontalInputGroup("", nextInvoiceDateValidate);

        // TODO a bootstrap 'form' would be handy where we pass individual components into it
        let $wrapper = $("<div>").addClass("form-horizontal");
        $wrapper.append(nextReviewDateGroup.element());

        createInvoiceBtn.clickSynchronous( () => {
            if (nextInvoiceDateValidate.isValid()) {
                this.createNewInvoice(this.nextInvoiceDateInput.getDate());
            }
        });
        let $createBtn = $("<div>").addClass("form-group").append($("<div>").addClass("col-xs-offset-6")
            .append(createInvoiceBtn.element()));
        $wrapper.append($createBtn);

        this.controlContainer.append($wrapper);
    }

    // TODO could enforce invoiceDate > lastInvoiceDate? as per ReviewChoicesControl.ts
    private createNewInvoice(invoiceDate: EccoDate) {
        let cmd = new CreateInvoiceCommand("add", this.serviceRecipientId)
            .changeInvoiceDate(null, invoiceDate);
        let cmdQueue = new CommandQueue(getCommandQueueRepository());
        cmdQueue.addCommand(cmd);
        cmdQueue.flushCommands()
            .then(() => this.submitted())
            .catch( (e) => {
                alert("failed to save your changes");
                throw e;
            });
    }

    public element() {
        return this.form.element();
    }

    public title() {
        return this._title;
    }

    public getFooter() {
        return this.footer.element();
    }

    private populateForm() {
        this.form.append(this.controlContainer);
    }

}

export default ServiceRecipientCreateInvoiceForm;
