import {SelectListOption} from "@eccosolutions/ecco-common";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {apiClient} from "ecco-components";
import {Question, QuestionAnswerFreeType, QuestionGroup, QuestionGroupAjaxRepository, SessionData} from "ecco-dto";
import {QuestionChangeCommand, QuestionChoiceChangeCommand} from "./commands";
import {
    EntryCell,
    EntryCellDropdown, EntryCellString,
    EntryRow,
    EntryRowNameOrderDisabled,
    ListEntryDto,
    ListOrderedControl
} from "./ListOrderedControl";

const repository = new QuestionGroupAjaxRepository(apiClient);

type QuestionType = "choices" | QuestionAnswerFreeType;

interface QuestionGroupWithUuid extends QuestionGroup {
    uuid: string; // this is simply 'id' as a string to avoid adding uuid to questiongroup sessionData
}

interface QuestionGroupListEntryDto extends ListEntryDto {
    type: QuestionType;
    listName: string;
}

/**
 * Parent is the question
 */
class EntryRowParent extends EntryRowNameOrderDisabled {

    private questionType: EntryCellDropdown;
    private listName: EntryCellString;
    // cmd is by name, the id is just for the value of the ddl
    private options: SelectListOption[] = [{name: "choices", id: 1}, {name: "integer", id: 2}, {name: "textarea", id: 3},
        {name: "checkbox", id: 4}, {name: "text", id: 5}, {name: "date", id: 6}, {name: "number", id: 7},
        {name: "list", id: 10},
        {name: "money", id: 8}, {name: "markdown", id: 9}];

    constructor(data: QuestionGroupListEntryDto) {
        super(data, data.id
            ? new QuestionChangeCommand("update", parseInt(data.parentDefUuid), parseInt(data.uuid))
            : new QuestionChangeCommand("add", parseInt(data.parentDefUuid), parseInt(data.uuid), data.orderby)
        );
        this.questionType = new EntryCellDropdown(this.getTypeId(data.type),
            (id) => this.changedType(id),
            "type",
            data.type == "integer" ? this.options : this.options.filter(o => o.name != "integer"),
            !!data.id);
        this.listName = new EntryCellString(data.listName, (value) => this.changedListName(value));
    }

    createEntryRowChildText(): string {
        return this.getTypeValue(this.questionType.getValue()) == "choices" ? "add new choice" : null;
    }

    public override getCells(): EntryCell<any>[] {
        return [this.name, this.questionType, this.listName, this.orderBy, this.disabled, this.id];
    }
    public override getCellsWidth(): number[] {
        return [5, 1, 1, 1, 2, 1];
    }

    public getEntryRowsChildren(): EntryRow[] {
        return this.listEntryDto.children.map(c => {
            return new EntryRowChild(c);
        });
    }

    public changedType(id: number) {
        const cmdQ = this.cmd as QuestionChangeCommand;
        // we can't change type, so fix from==to by supplying null
        cmdQ.changeType(null, this.getTypeValue(id));
        this.triggerChangeEvent();
    }

    public changedListName(listName: string) {
        const cmdQ = this.cmd as QuestionChangeCommand;
        // we can't change type, so fix from==to by supplying null
        cmdQ.changeListName(null, listName);
        this.triggerChangeEvent();
    }

    // 'child' is the question answer choices
    public createEntryRowChild(orderBy: number) {
        const dto: ListEntryDto = {
            id: null,
            uuid: Uuid.randomV4().toString(), // null, but perhaps useful as a reference
            parentDefUuid: this.listEntryDto.uuid.toString(),
            name: "",
            orderby: orderBy,
            disabled: false,
            children: []
        };
        return new EntryRowChild(dto);
    }

    private getTypeValue(id: number): string {
        const first = this.options.find(o => o.id == id);
        return first ? first.name : null;
    }

    private getTypeId(value: string): number {
        const first = this.options.find(o => o.name == value);
        return first ? <number>first.id : null;
    }
}

/**
 * Child is the questionanswerchoice
 */
class EntryRowChild extends EntryRowNameOrderDisabled {

    constructor(data: ListEntryDto) {
        super(data, data.id
            ? new QuestionChoiceChangeCommand("update", parseInt(data.parentDefUuid), parseInt(data.uuid))
            : new QuestionChoiceChangeCommand("add", parseInt(data.parentDefUuid), null, data.orderby));
    }

    public createEntryRowChildText(): string {
        return null;
    }

    public getEntryRowsChildren(): EntryRow[] {
        return this.listEntryDto.children.map(c => {
            return new EntryRowChild(c);
        });
    }

    public createEntryRowChild(orderBy: number) {
        return null;
    }
}

class QuestionGroupControl extends ListOrderedControl<QuestionGroupWithUuid> {

    private questionGroup: QuestionGroupWithUuid;

    constructor(private questionGroupId: number) {
        super();
    }

    getManageEntityText(): string {
        return "question groups";
    }

    public getNewParentEntryRowText(): string {
        return "add new question";
    }

    protected fetchViewData(): Promise<QuestionGroupWithUuid> {
        return repository.findOneQuestionGroup(this.questionGroupId).then(qg => {
            this.questionGroup = <QuestionGroupWithUuid> qg;
            this.questionGroup.uuid = qg.id.toString();
            return this.questionGroup;
        });
    }

    public getParentEntryRows(data: QuestionGroup)  {
        const rows = data.questions.map((q: Question) => {
            const typeQn = SessionData.questionType(q, true) as any as QuestionType;
            const conv: QuestionGroupListEntryDto = {
                id: q.id,
                uuid: q.id.toString(),
                name: q.name,
                listName: q.parameters?.listName,
                parentDefUuid: data.id.toString(), // for consistency
                orderby: q.orderby,
                type: typeQn,
                // answerRequired:
                disabled: q.disabled,
                children: []
            };
            if (typeQn == "choices") {
                const chd = q.choices.map(c => {
                    const child: ListEntryDto = {
                        id: c.id,
                        uuid: c.id.toString(),
                        name: c.displayValue,
                        parentDefUuid: q.id.toString(), // for consistency
                        orderby: null,
                        disabled: c.disabled,
                        children: []
                    };
                    return child;
                });
                conv.children = conv.children.concat(chd);
            }
            return new EntryRowParent(conv);
        });
        return rows;
    }

    // get new question in questiongroup
    public getNewParentEntryRow(data: QuestionGroup): EntryRow {
        const uuid = Uuid.randomV4().toString();
        const dto: QuestionGroupListEntryDto = {
            id: null,
            uuid: uuid,
            parentDefUuid: data.id.toString(),
            name: "",
            listName: null,
            type: null,
            orderby: null,
            disabled: false,
            children: []
        };
        return new EntryRowParent(dto);
    }
}
export default QuestionGroupControl;
