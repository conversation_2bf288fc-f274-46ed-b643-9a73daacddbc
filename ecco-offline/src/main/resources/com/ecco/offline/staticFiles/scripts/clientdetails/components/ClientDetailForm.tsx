import * as React from "react"
import {
    AsyncServiceRecipientWithEntities,
    ClientDetailEditor
} from "ecco-components";

import {showInCommandForm} from "../../components/CommandForm";
import {TaskNames} from "ecco-dto";


/**
 * LOAD FORM - non-jsx, jsp
 */
export function clientDetail(serviceRecipientId: number, showCode: boolean, onCompleted: () => void) {
    showInCommandForm(
            <AsyncServiceRecipientWithEntities srId={serviceRecipientId}>
                <ClientDetailEditor serviceRecipientId={serviceRecipientId} taskName={TaskNames.clientWithContact} taskHandle={null} formRef={() => {}} showCode={showCode}/>
            </AsyncServiceRecipientWithEntities>,
            onCompleted
    );
}
