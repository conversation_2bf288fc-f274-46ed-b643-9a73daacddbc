import * as React from "react";

import {ErrorBoundary} from "ecco-components-core";
import {ReferralsWithEventsContext, ReferralsWithEventsLoadAndRender} from "./CalendarListLoader";
import {EventResourceDto, ReferralDto} from "ecco-dto";
import {EventCard} from "../components/EventCard";
import {Grid} from '@eccosolutions/ecco-mui';
import {mountWithServices} from "../offline/ServicesContextProvider";


// NOTE: Params are always string so need parsing to number where needed
interface Props {
    chartDefUuid: string;
}

interface State {
    loaded?: boolean | undefined;
    //data?: ReferralSummaryDto[];
}

// called by CalendarEventReportStageControl from the 'project calendar' report with: "stageType": "CALENDAREVENT"
export function renderEvents(el: Element, events: EventResourceDto[]) {
    mountWithServices(
        <Grid container>
            {events.map((e: EventResourceDto) => <Grid item className="card" lg={3} md={4} sm={6} xs={12}>
                <EventCard key={e.uid} event={e}/>
            </Grid>)}
        </Grid>,
        el
    );
}

export class CalendarListFrame extends React.Component<Props, State>  {

    constructor(props) {
        super(props);
        this.state = {
            loaded: false
            //data: null
        };
    }

    override render() {
        const events = (referrals: ReferralDto[]) =>
            referrals.filter(r => (r.calendarEvents != null) && (r.calendarEvents.length > 0))
                .map(r => r.calendarEvents
                    .map((e: EventResourceDto) => <Grid item className="card" lg={3} md={4} sm={6} xs={12}>
                        <EventCard key={e.uid} event={e}/>
                    </Grid>)
                );

        return (
            <ErrorBoundary>
                <ReferralsWithEventsLoadAndRender chartDefUuid={this.props.chartDefUuid}>
                    {(context: ReferralsWithEventsContext) =>
                        events(context.data)
                    }
                </ReferralsWithEventsLoadAndRender>
            </ErrorBoundary>
        );
    }
}
