import * as React from "react";
import {FC, useMemo} from "react";
import {GroupActivityList, GroupActivityOptions} from "./GroupActivityList";
import GroupAuxActivityListControl from "./GroupAuxActivityListControl";
import {GroupActivityCommand} from "./commands";
import {GROUPAUXACTIVITY_LISTNAME} from "ecco-dto";
import {GroupPageType} from "ecco-dto";

export const groupAuxActivityOptions: GroupActivityOptions = {
    discriminator_orm: GroupActivityCommand.DISCRIMINATOR_AUX,
    title: "accident / incident",
    hasActivityType: false,
    hasVenue: false,
    hasCapacity: false,
    hasDuration: false,
    allowAttended: false,
    addService: false,
    hasCategory: true,
    hasReviewDate: true,
    activityListName: GROUPAUXACTIVITY_LISTNAME,
    createListControl: (groupPageType: GroupPageType, venueId: number, serviceId: number) => new GroupAuxActivityListControl(groupPageType, venueId, serviceId)
}

/** entry point to the groups */
export const GroupAuxActivityList: FC = () => {
    const options = useMemo(() => groupAuxActivityOptions, []);
    return <GroupActivityList options={options}/>;
}
