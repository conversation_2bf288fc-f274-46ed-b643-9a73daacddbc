import $ = require("jquery");
import moment = require("moment");
import * as events from "../common/events";

import BaseAsyncDataControl from "../controls/BaseAsyncDataControl";
import TabbedContainer from "../controls/TabbedContainer";
import * as SessionDataService from "../feature-config/SessionDataService";
import {EccoDate, EccoDateTime} from "@eccosolutions/ecco-common";
import {apiClient} from "ecco-components";
import {GroupActivityDto, SessionData} from "ecco-dto";
import {GroupActivity, GroupSupportAjaxRepository} from "ecco-dto";
import {groupActivity} from "./GroupSupportActivityEdit";
import {attachmentsEnhance} from "../attachments/AttachmentsControl";
import {loadServiceRecipientAndAttach} from "../workflow/tasklist/TasksControl";
import {GroupActivityOptions} from "./GroupActivityList";
import {GroupSupportActivityListControl} from "./GroupSupportActivityListControl";

const repository = new GroupSupportAjaxRepository(apiClient);


class BackingData {
    constructor(public sessionData: SessionData, public activity: GroupActivity, public options: GroupActivityOptions) {
    }
}

abstract class GroupActivityOverviewControl extends BaseAsyncDataControl<BackingData> {

    private tabs: TabbedContainer;

    private attendanceDateRange: EccoDate[];

    constructor(private activityId: number) {
        super();
    }

    protected fetchViewData(): Promise<BackingData> {
        return repository.findOneActivity(this.activityId).then( activity => {
             return SessionDataService.SessionDataService.getFeatures().then( (sessionData) => {
                 return new BackingData(sessionData, activity, this.options());
             });
         });
    }

    protected render(data: BackingData) {
        this.tabs = new TabbedContainer();
        this.element()
            .empty()
            .append(this.tabs.element());

        let activity = data.activity;
        if (!this.attendanceDateRange) {
            this.attendanceDateRange = GroupActivityOverviewControl.buildDateRange(activity.getStartDateTime(), activity.getEndDate());
        }

        if (activity.getDto().course) {
            this.renderChildren(activity.getDto().id);
        }

        this.renderClients(data.sessionData, activity);

        this.renderTasks(activity.getServiceRecipientId());
        this.renderAttachments(activity.getServiceRecipientId());

        //this.renderAttendanceWithEndDate(activity);

        this.setMenu(activity, data);
    }

    private renderAttachments(srId: number) {
        var tab = $("<div>").addClass("container-fluid top-gap-15 bottom-gap-15");

        this.tabs.append("attachments", "attachments", tab);

        var $container = $("<div/>");
        attachmentsEnhance($container, srId);
        tab.append($container);
    }

    private renderTasks(srId: number) {
        var tab = $("<div>").addClass("container-fluid top-gap-15 bottom-gap-15");
        this.tabs.append("tasks", "pathway", tab);
        loadServiceRecipientAndAttach(tab.get(0), srId);
    }

    private renderChildren(parentId: number) {
        var tab = $("<div>").addClass("container-fluid top-gap-15 bottom-gap-15");
        this.tabs.append("sessions", "sessions", tab);
        var $container = $("<div/>");

        // button for 'new session'...
        const editEl = $("<div>").css({"text-align": "right"});
        groupActivity(this.options(), null, parentId, editEl[0], () => this.load());
        $container.append(editEl)


        var ctl = new GroupSupportActivityListControl("sessions", null, null, null, parentId)
        $container.append(ctl.element());
        ctl.load();
        tab.append($container);
    }

    protected abstract label(lookup: string): string;
    protected abstract options(): GroupActivityOptions;

    protected abstract createClientListControl(sessionData: SessionData, activity: GroupActivityDto);

    private renderClients(sessionData: SessionData, activity: GroupActivity) {

        // Show those eligible for a group support activity
        const control = this.createClientListControl(sessionData, activity.getDto());

        var tab = $("<div>").addClass("container-fluid top-gap-15 bottom-gap-15");

        this.tabs.append("clients", "clients", tab);

        var $link = $("<a>").text(this.label("register")).click(() => {
            if (control.isPrintable()) {
                $link.text(this.label("register"));
                control.setPrintable(false);
            } else {
                $link.text("back to full list");
                control.setPrintable(true);
            }
        });
        tab.append($link);

        // tab.append(
        //     $('<p>').append("Note: Clients must be 'live' on the selected service to be listed here") );

        if (activity.getDto().endDate) {
            /*var $note = $('<p>').append('Note: there is no need to invite clients for a daily register. '
                + 'They will show as invited as soon as they are recorded as having attended on the attendance tab');
            tab.append($note);*/
        }

        tab.append(control.element());
        control.load();
    }

    // DISABLED
    /*private renderAttendanceWithEndDate(activity: GroupActivity) {
        // only load ClientAttendanceWithEndDateListControl if there is an end date
        if (!activity.getEndDate()) {
            return; // Only show attendance for ongoing activities
        }
        const tab = $("<div>").addClass("container-fluid top-gap-15 bottom-gap-15");
        this.tabs.append("attendance", this.label("attendance"), tab);

        var $nav = $('<nav>');
        var $pager = $('<ul class="pager">');

        var $label = $('<h4 class="inline">').append(this.formattedAttendanceDateRange());
        $pager.append($label);

        var $li = $('<li class="previous">');
        var $previous = $('<a>Previous</a>');
        $previous.on('click', (event: $.BaseJQueryEventObject) => {
            event.preventDefault();
            this.moveAttendanceToDateRange(-7);
            $label.empty().append(this.formattedAttendanceDateRange());
            control.loadWithDateRange(this.attendanceDateRange[0], this.attendanceDateRange[1]);
        });
        $li.append($previous);
        $pager.append($li);

        $li = $('<li class="next">');
        var $next = $('<a>Next</a>');
        $next.on('click', (event: $.BaseJQueryEventObject) => {
            event.preventDefault();
            this.moveAttendanceToDateRange(7);
            $label.empty().append(this.formattedAttendanceDateRange());
            control.loadWithDateRange(this.attendanceDateRange[0], this.attendanceDateRange[1]);
        });
        $li.append($next);
        $pager.append($li);

        $nav.append($pager);
        tab.append($nav);

        var control = new ClientAttendanceWithEndDateListControl(activity);
        tab.append(control.element());
        control.loadWithDateRange(this.attendanceDateRange[0], this.attendanceDateRange[1]);
    }*/

    public static buildDateRange(start: EccoDateTime, end: EccoDate | null): EccoDate[] {
        var targetDay = EccoDate.todayLocalTime();

        // narrow target day to be within the date range
        var startDate = start.toEccoDate();
        if (targetDay.earlierThan(startDate)) {
            targetDay = startDate;
        }

        var endDate = end;
        if (endDate && targetDay.laterThan(endDate)) {
            targetDay = endDate;
        }

        var firstDayOfWeek = targetDay.subtractDays(targetDay.getDayOfWeek() -1);
        return [firstDayOfWeek, firstDayOfWeek.addDays(6)];
    }

    /*private moveAttendanceToDateRange(days: number): EccoDate[] {
        this.attendanceDateRange[0] = this.attendanceDateRange[0].addDays(days);
        this.attendanceDateRange[1] = this.attendanceDateRange[1].addDays(days);
        return this.attendanceDateRange;
    }*/

    /*private formattedAttendanceDateRange(): string {
        return "from " + this.attendanceDateRange[0].formatShort()
            + " to " + this.attendanceDateRange[1].formatShort();
    }*/

    private setMenu(activity: GroupActivity, data: BackingData) {
        var $menu = $("<div>").addClass("text-center");

        var $menuLine = $("<h2>");
        $menuLine.append(activity.getDto().course ? "course" : "session");
        $menu.append($menuLine);

        $menuLine = $("<div>");
        if (data.options.hasVenue && activity.getDto().venueName) {
            $menuLine.append(activity.getDto().venueName);
            $menuLine.append(" - ");
        }
        $menuLine.append(moment(activity.getDto().startDateTime).format("DD MMMM YYYY HH:mm"));
        $menu.append($menuLine);

        $menuLine = $("<div>");
        $menuLine.append(activity.getDto().description);
        $menu.append($menuLine);

        $menuLine = $("<div>");
        if (data.options.hasCapacity && activity.getDto().capacity > 0) {
            $menuLine.append("capacity: " + activity.getDto().capacity);
        }
        if (data.options.hasDuration && activity.getDto().duration > 0) {
            $menuLine.append(" minutes: " + activity.getDto().duration);
        }
        $menu.append($menuLine);

        // activity details
        $menuLine = $("<div>");
        const editEl = document.createElement("span");
        groupActivity(data.options, this.activityId, null, editEl, () => this.load());
        /* was the following that had extra features for daycentre clients
            UNUSED
          $editLink.on("click", (ev) => GroupSupportActivityEditWithDemandControl.showInModal(this.activityId));
         */
        $menuLine.append(editEl);
        $menu.append($menuLine);

        // parent course link?
        // if (activity.getDto().parentId) {
        //     $menuLine = $("<div>");
        //     $menu.append($menuLine);
        // }

        //$menuLine = $("<div>").addClass("text-left");
        //var $backLink = $("<a>back to list</a>");
        //$backLink.attr("href", "../")
        //$menuLine.append($backLink);
        //$menu.append($menuLine);

        // called from WelcomeAppBar with GroupSupportActivityOverviewModal
        events.MenuUpdateEvent.bus.fire( new events.MenuUpdateEvent("nav", $menu) );
    }
}
export default GroupActivityOverviewControl;
