import {IdName} from "@eccosolutions/ecco-common";
import {apiClient} from "ecco-components";
import {ServiceCategorisation} from "ecco-dto";
import * as _ from "lodash";
import {NumericDictionary} from "lodash";
import * as React from "react";
import {ReactEventHandler} from "react";
import {Form, FormControl, FormGroup} from "react-bootstrap";
import * as ReactDom from "react-dom";
import {EntityRestrictionsAjaxRepository} from "./EntityRestrictionsAjaxRepository";

const restrictedEntityRepository = new EntityRestrictionsAjaxRepository(apiClient);

interface ServiceCatSelectProps {
    serviceCategorisations: Array<ServiceCategorisation>;
    onSelect: (svcCatId: number | null) => void;
}

interface State {
    serviceGroupId: number | null;
    serviceCategorisationId: number | null;
    subSelect: {}[] | null;
}

function idNamesToOptions(entries: IdName[]) {
    return entries.map((entry, idx) => <option key={entry.id} value={entry.id}>{entry.name}</option>);
}

function intFromHtmlInput(eventTarget: EventTarget): number | null {
    let value = (eventTarget as HTMLInputElement).value;
    return value ? parseInt(value, 10) : null;
}

export function attach(el: HTMLElement,
                       onSelect: (selected: ServiceCategorisation) => void
) {
    restrictedEntityRepository.findRestrictedServicesCategorisations().then(serviceCats => {
        const expandSelected = id => onSelect(serviceCats.filter(sc => sc.id == id)[0]);
        ReactDom.render(<ServiceCatSelect serviceCategorisations={serviceCats.filter(sc => !sc.disabled && sc.projectId != null)} onSelect={expandSelected}/>, el);
    });
}

// shown when 'referral.list.service-group' is on
export default class ServiceCatSelect extends React.Component<ServiceCatSelectProps, State> {
    private serviceGroups: IdName[];
    private catsByServiceGroupId: NumericDictionary<ServiceCategorisation[]>;


    constructor(props: ServiceCatSelectProps) {
        super(props);
        this.onSelectService = this.onSelectService.bind(this);

        this.state = {
            serviceGroupId: null,
            serviceCategorisationId: null,
            subSelect: null
        };

        this.catsByServiceGroupId = _.groupBy(this.props.serviceCategorisations, sc => sc.serviceGroupId || -2);
        this.serviceGroups = Object.values(this.catsByServiceGroupId)
            .map( v => ({
                id: v[0].serviceGroupId || -2,
                name: v[0].serviceGroupName || '(without service group)',
                disabled: false
            }));
    }

    onSelectService: ReactEventHandler<FormControl> = event => {
        const serviceGroupId = intFromHtmlInput(event.target);
        const subSelect = serviceGroupId == null ? []
            : this.catsByServiceGroupId[serviceGroupId].map(
            (item, idx: number) => (
                // TODO: Remove .toString() with React v15 typings
                <option key={idx} value={item.id.toString()}>{item.serviceName + ' - '}{item.projectName || 'no project'} ({item.companyName || 'no company'})</option>
            )
        );
        this.setState({
            serviceGroupId,
            serviceCategorisationId: null,
            subSelect
        });
    }

    onSelectCategorisation: ReactEventHandler<FormControl> = event => {
        let id = intFromHtmlInput(event.target);
        this.setState({serviceCategorisationId: id});
        this.props.onSelect(id);
    }

    override render() {
        return (
            <Form inline={true}>
                <FormGroup controlId="serviceGroupSelect">
                    {/*<ControlLabel>Service:</ControlLabel>*/}
                    <FormControl
                        componentClass="select"
                        onChange={this.onSelectService}
                        value={this.state.serviceGroupId || ''}
                    >
                        <option value="">- Select Service Group -</option>
                        {idNamesToOptions(this.serviceGroups)}
                    </FormControl>
                </FormGroup>
                {this.state.serviceGroupId && (
                    <FormGroup controlId="serviceGroupSelect">
                        {/*<ControlLabel>Project:</ControlLabel>*/}
                        <FormControl
                            componentClass="select"
                            onChange={this.onSelectCategorisation}
                            value={this.state.serviceCategorisationId || ''}
                        >
                            <option value="">- Select Project -</option>
                            {this.state.subSelect}
                        </FormControl>
                    </FormGroup>
                )}
            </Form>
        );
  }
}
