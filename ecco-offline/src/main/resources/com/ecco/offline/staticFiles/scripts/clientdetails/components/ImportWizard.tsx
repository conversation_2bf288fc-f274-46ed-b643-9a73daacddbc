import * as React from "react"
import {StringUtils} from "@eccosolutions/ecco-common";
import {applicationRootPath} from "application-properties";
import {apiClient, withSessionData} from "ecco-components";
import {Client, ClientAjaxRepository} from "ecco-dto";
import {<PERSON>ton, ButtonToolbar} from "react-bootstrap";
import ClientDetailNew from "./ClientDetailNew";

/**
 * NB this file hasn't really been touched from 2016
 * Triggered by /nav/r/clients/import/:externalSourceName/:externalRef
 * see clientdetails/router
*/

const STATUS_SAVED = "Saved data. Loading client record...";
const STATUS_SEARCHING = "Searching for a client with that reference...";

export type ImportWizardRouteParams = {
    externalSourceName: string;
    externalRef: string;
    x: string;
};

interface Props extends React.ClassAttributes<ClientWizard> {
    params: ImportWizardRouteParams;
}

type Step = "search" | "import" | "complete";

function buildSteps(): Step[] {
    return ["search", "import", "complete"];
}

function validate(client: Client) {
    const errors: any = {};
    if (!client.clientId) errors['clientId'] = 'required';
    return errors;
}

interface State {
    client: Client | null;
    errors: {};
    steps: Step[];
    stepSelected: Step;
    status: string;
    httpError?: string | undefined;
}

class ClientWizard extends React.Component<Props, State> {
    private repository = new ClientAjaxRepository(apiClient);

    constructor(props: Props) {
        super(props);

        this.state = {
            client: null,
            errors: {},
            steps: buildSteps(),
            stepSelected: "search",
            status: STATUS_SEARCHING
        };
    }

    public override componentDidMount() {
        window.onbeforeunload = function() {
            return "Your data will be lost";
        };
        // if chosen 'import', reload the data to set the first/last name
        this.repository.findOneExternalClientBySourceAndRef(this.props.params.externalSourceName, this.props.params.externalRef)
            .then(client => {
                client.firstName = client.firstName && StringUtils.sanitiseCase(client.firstName);
                client.lastName = client.lastName && StringUtils.sanitiseCase(client.lastName);
                this.setState({client: client});
            })
            .catch(reason => {
                this.setState({httpError: reason.reason && reason.reason.message});
            });
    }

    public override componentWillUnmount() {
        window.onbeforeunload = null;
    }


    private handleDoneClick = () => {
        this.saveClient();
    };

    private handleNextClick = () => {
        this.setState({stepSelected: this.nextStep()});
    };

    private handlePreviousClick = () => {
        this.setState({stepSelected: this.previousStep()});
    };

    private saveClient(client?: Client | undefined) {
        client = client || this.state.client;

        // already saved
        if (client.clientId) {
            // redirect
            window.onbeforeunload = null;
            window.location.href = `${applicationRootPath}nav/secure/entity/clients/${client.clientId}`;
        }

        return this.repository.saveClient(client).then(response => {
            this.setState({status: STATUS_SAVED});

            // redirect
            window.onbeforeunload = null;
            window.location.href = `${applicationRootPath}nav/secure/entity/clients/${response.id}`;
        });
    }

    private handleClientSave(client: Client) {

        const errors = validate(client);
        const stepSelected = this.isStepValid(errors) && this.nextStep() || this.state.stepSelected;

        this.setState({
            client: client,
            errors: errors,
            stepSelected: stepSelected
        });
    }

    private handleModalCancel = () => {
        this.setState({
            stepSelected: this.previousStep()
        });
    };

    private isStepValid(errors?: any): boolean {
        errors = errors || this.state.errors;

        switch (this.state.stepSelected) {
            case "import":
                return !errors.clientId;
        }
        return true
    }

    private hasNext(): boolean {
        const stepIndex = this.state.steps.indexOf(this.state.stepSelected);
        return stepIndex < this.state.steps.length - 1;
    }

    private hasPrevious(): boolean {
        const stepIndex = this.state.steps.indexOf(this.state.stepSelected);
        return stepIndex > 0;
    }

    private nextStep(steps?: Step[] | undefined): Step {
        steps = steps || this.state.steps;
        const stepIndex = steps.indexOf(this.state.stepSelected);
        return steps[stepIndex + 1];
    }

    private previousStep(): Step {
        const stepIndex = this.state.steps.indexOf(this.state.stepSelected);
        return this.state.steps[stepIndex - 1];
    }

    override render() {
        let StepElement: React.ReactElement;
        let StepHeaderElement: React.ReactElement;
        let PreviousButtonElement: React.ReactElement;
        let NextButtonElement: React.ReactElement;

        if (this.hasPrevious()) {
            PreviousButtonElement = (
                <Button
                    onClick={this.handlePreviousClick}>
                    previous
                </Button>
            );
        }

        if (this.hasNext()) {
            NextButtonElement = (
                <Button
                    onClick={this.handleNextClick}
                    disabled={!this.isStepValid()}
                    bsStyle="primary">
                    next
                </Button>
            );
        } else {
            NextButtonElement = (
                <Button
                    onClick={this.handleDoneClick}
                    disabled={!this.isStepValid() || this.state.status == STATUS_SAVED}
                    bsStyle="primary">
                    {this.state.status == STATUS_SAVED && STATUS_SAVED || 'done'}
                </Button>
            );
        }

        switch (this.state.stepSelected) {
            case "search":
                if (this.state.httpError) {
                    StepElement = (
                        <div>
                            <h4 className="page-header">{this.props.params.externalSourceName} client with reference {this.props.params.externalRef}</h4>
                            <div className="alert alert-warning">Search failed: {this.state.httpError}</div>
                        </div>
                    );
                }
                else if (this.state.client == null) {
                    StepElement = (
                        <p className="text-center">{this.state.status == STATUS_SEARCHING ? "loading..." : "no match found"}</p>
                    );
                }
                else {
                    StepElement = (
                        <div>
                            <h4 className="page-header">Import client</h4>
                            <p><strong>{this.state.client.firstName} {this.state.client.lastName}</strong> does
                            not yet have a client record in ECCO.</p>
                            <p>Click 'next' to import their details and get them started.</p>
                        </div>
                    );
                }
                break;
            case "import":
                StepHeaderElement = (
                    <h3>client</h3>
                );
                StepElement = withSessionData(sessionData =>
                    <ClientDetailNew
                        sessionData={sessionData}
                        client={this.state.client}
                        show={this.state.stepSelected == "import"}
                        showAsModal={true}
                        onSave={client => this.handleClientSave(client)}
                        onCancel={this.handleModalCancel}/>
                );
                NextButtonElement = null;
                break;
            case "complete":
                StepHeaderElement = (
                    <h3>data capture completed</h3>
                );
                if (this.state.client.clientId) {
                    StepElement = (
                        <div className='row'>
                            <div className='col-xs-12'>
                                <div className='page-header'>
                                    <h3>client saved</h3>
                                    <p>press 'done' to open the client</p>
                                </div>
                            </div>
                        </div>
                    );
                }
                else {
                    StepElement = (
                        <div className='row'>
                            <div className='col-xs-12'>
                                <div className='page-header'>
                                    <h3>new client is ready to be saved</h3>
                                    <p>press 'done' to save and open the client</p>
                                </div>
                            </div>
                        </div>
                    );
                }
                break;
        }
        let FooterElement = null;
        if (this.state.client) {
            FooterElement =
                (<div>
                    <ButtonToolbar className='pull-right'>
                        {PreviousButtonElement}
                        {NextButtonElement}
                    </ButtonToolbar>
                </div>);
        }

        return (
            <div>
                {StepElement}
                {FooterElement}
            </div>
        );
    }

}

export default ClientWizard;
