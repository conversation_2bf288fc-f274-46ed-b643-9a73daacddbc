import $ = require("jquery");
import BaseAsyncListControl from "../controls/BaseAsyncListControl";
import BaseListEntryControl from "../controls/BaseListEntryControl";
import EditOutcomeForm from "./EditOutcomeForm";
import {apiClient} from "ecco-components";
import {OutcomeDto} from "ecco-dto";
import {OutcomeAjaxRepository} from "./OutcomeAjaxRepository";
import URI from "URI";

const repository = new OutcomeAjaxRepository(apiClient);


class EntryControl extends BaseListEntryControl<OutcomeDto> {

    constructor(outcome: OutcomeDto) {
        super(outcome, "fa fa-star-o");
    }

    protected administerEntry(): void {
        EditOutcomeForm.showInModal(this.entry.id);
    }

    protected getEditElement(): $.JQuery {
        let uri = URI(window.location.href)
        return $("<a>").attr("href", `${uri}${this.entry.id}/`).text(this.entry.name)
    }

    protected getEntryIconClass(): string {
        return "fa fa-star-o";
    }

}

// TODO: Extract base AdminCapableListControl from this and ChartListControl
class OutcomesListControl extends BaseAsyncListControl<OutcomeDto> {

    constructor() {
        super("add new outcome", "no outcomes defined", "fa fa-star-o");
    }

    protected fetchViewData(): Promise<OutcomeDto[]> {
        return repository.findAllOutcomeSupports();
    }

    protected createItemControl(outcome: OutcomeDto) {
        return new EntryControl(outcome);
    }

    protected addNewEntity() {
        EditOutcomeForm.showInModal(null);
    };

}
export default OutcomesListControl;
