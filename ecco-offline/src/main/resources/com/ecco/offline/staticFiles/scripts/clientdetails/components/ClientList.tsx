import * as React from "react"
import {Component, FC, MutableRefObject, ReactElement} from "react"
import {StringToObjectMap} from "@eccosolutions/ecco-common";
import {ErrorBoundary, showReactInModal, SelectList} from "ecco-components-core";
import {
    apiClient,
    AsyncSessionData,
    PersonSearchBar,
    useServicesContext,
    withSessionData
} from "ecco-components";
import {
    Client,
    ClientAjaxRepository,
    DelegateResponse,
    PersonSearchCriteriaDto,
    ReferralSummaryDto,
    SessionData,
    TaskNames
} from "ecco-dto";
import {Button} from "react-bootstrap";
import {SearchAndImportList} from "../../components/SearchAndImportList";
import {SessionDataService} from "../../feature-config/SessionDataService";
import {ServicesContextProvider} from "../../offline/ServicesContextProvider";
import ClientDetailNew from "./ClientDetailNew";
import ClientReferralsPopup from "./ClientReferralsPopup";
import {NewReferralWizard} from "../../referral/components/NewReferralWizard";
import {BaseUpdateCommand, CommandQueue, ServiceRecipientAssociatedContactCommand} from "ecco-commands";
import {showErrorAsAlert} from "ecco-offline-data";
import * as environment from "../../environment";
import services = require("ecco-offline-data");
import {IdNameDisabled} from "@eccosolutions/ecco-common";

const ClientListWithListControl: FC<{listDefs: IdNameDisabled[], onChange: (relationship: number | null) => void}> = props => {

    //const [relationship, setRelationship] = useState(null);
    const onChange = (o: any) => {
        props.onChange(o && parseInt(o.value, 10));
    }

    const entries: Array<{value: string, label: string}> =
        props.listDefs.map(entry => ({value: entry.id.toString(), label: entry.name}));

    // onChange={value => areaChange(value as ListDefinitionEntry)}
    return <>
        <SelectList
            createNew={false}
            options={entries}
            onChange={onChange}
        />
        {/* make some space, otherwise the select list options have no room to show */}
        <div style={{marginTop: "200px"}} />
    </>
};

/** RELATIONSHIP choice for the client referral **/
const RelationshipControl: FC<{onChange: (relationship: number | null) => void}> = props => {
    const {sessionData} = useServicesContext();

    return <ClientListWithListControl listDefs={sessionData.getRelationshipAssociationTypesList()} onChange={props.onChange}/>
}

interface ClientListWithProps {
    primaryServiceRecipientId: number;
    sessionData: SessionData;
    existingOnly: boolean;
    subFormsAsModal: boolean;
    onClientChange: () => void;
    onSelectedReferral: (referral: ReferralSummaryDto) => void;
}
interface ClientListWithState {
    listId: number;
}

abstract class ClientListWith extends Component<ClientListWithProps, ClientListWithState> {

    constructor(props) {
        super(props);
        this.state = {
            listId: null
        };
    }

    protected sendCommand(cmd: BaseUpdateCommand): Promise<void> {
        const commandQueue = new CommandQueue();
        commandQueue.addCommand(cmd);
        return commandQueue.flushCommands(false)
            .catch(e => showErrorAsAlert(e));
    }

}

export class ClientListWithRelationship extends ClientListWith {

    public static addRelationshipClientReferralModal(primaryServiceRecipientId: number, existingOnly: boolean, onSelectedReferral: (referral: ReferralSummaryDto) => void) {

        const cancelRef = {} as MutableRefObject<() => void>;

        const content = withSessionData(sessionData =>
            <ClientListWithRelationship
                primaryServiceRecipientId={primaryServiceRecipientId}
                subFormsAsModal={false} sessionData={sessionData} existingOnly={existingOnly}
                onClientChange={() => cancelRef.current()} // ideally we shouldn't pass closing of a dialog, but works for now
                onSelectedReferral={onSelectedReferral}
            />
        );
        showReactInModal("add relationship",
            <ErrorBoundary><AsyncSessionData promiseFn={SessionDataService.getFeatures}><ServicesContextProvider>
                {content}
            </ServicesContextProvider></AsyncSessionData></ErrorBoundary>,
             {
                 action: "cancel",
                 saveEnabled: false,
                 unmountCallbackRef: cancelRef
             }
         );
    }

    override render() {

        const onClientChange = (sessionData: SessionData, client: Client, isNew: boolean) => {
            this.props.onClientChange();

            // choose a service for the relationship
            // this is based on a service type setting for 'multipleReferralService' ReferralFlowAction#setupNewRelationshipReferral relied on specific service types named as multipleReferralService_" + relationship
            services.getReferralRepository().findOneReferralSummaryByServiceRecipientIdUsingDto(this.props.primaryServiceRecipientId).then(ref => {
                const serviceIdSettings = this.props.sessionData.getServiceTypeByServiceCategorisationId(ref.serviceAllocationId).getTaskDefinitionEntry(TaskNames.newMultipleReferral);
                const serviceIdDefault = serviceIdSettings.getSetting( "multipleReferralService");
                const serviceIdSpecific = serviceIdSettings.getSettings().filter(s => s.getName() ==  "multipleReferralService_" + this.state.listId.toString());
                let serviceId = serviceIdSpecific.length > 0
                        ? parseInt(serviceIdSpecific[0].getValue())
                        : serviceIdDefault ? parseInt(serviceIdDefault) : sessionData.getServiceCategorisation(ref.serviceAllocationId).serviceId;

                const onSelected = (referral) => {
                    const cmd = new ServiceRecipientAssociatedContactCommand("add", this.props.primaryServiceRecipientId, referral.contactId);
                    cmd.withAssociatedServiceRecipientId(referral.serviceRecipientId);
                    cmd.changeAssociatedRelationshipType(null, this.state.listId);
                    this.sendCommand(cmd).then(() =>
                        this.props.onSelectedReferral(referral)
                    );
                };

                NewReferralWizard.popup(client.clientId, onSelected, undefined, undefined, serviceId);
            })

        };

        return !this.state.listId
            ? <RelationshipControl onChange={(listId) => this.setState({listId})}/>
            : <ClientList
                    sessionData={this.props.sessionData}
                    subFormsAsModal={this.props.subFormsAsModal}
                    existingOnly={this.props.existingOnly}
                    onChange={(client, isNew) => onClientChange(this.props.sessionData, client, isNew)}
                    selectedResultRender={false}
            />
    }

}

export class ClientListWithIncident extends ClientListWith {

    public static addIncidentClientReferralModal(primaryServiceRecipientId: number, existingOnly: boolean, onSelectedReferral: (referral: ReferralSummaryDto) => void) {

        const cancelRef = {} as MutableRefObject<() => void>;

        const content = withSessionData(sessionData =>
            <ClientListWithIncident
                primaryServiceRecipientId={primaryServiceRecipientId}
                subFormsAsModal={false} sessionData={sessionData}
                existingOnly={existingOnly}
                onClientChange={() => cancelRef.current()} // ideally we shouldn't pass closing of a dialog, but works for now
                onSelectedReferral={onSelectedReferral}
            />
        );
        showReactInModal("link client",
            <ErrorBoundary><AsyncSessionData promiseFn={SessionDataService.getFeatures}><ServicesContextProvider>
                {content}
            </ServicesContextProvider></AsyncSessionData></ErrorBoundary>,
             {
                 action: "cancel",
                 saveEnabled: false,
                 unmountCallbackRef: cancelRef
             }
         );
    }

    override render() {

        const onClientChange = (sessionData: SessionData, client: Client, isNew: boolean) => {
            this.props.onClientChange();

            const onSelected = (linkContactId) => {
                const cmd = new ServiceRecipientAssociatedContactCommand("add", this.props.primaryServiceRecipientId, linkContactId);
                cmd.changeAddedAssociatedTypeIds(null, this.state.listId.toString());
                this.sendCommand(cmd).then(() => {
                    //this.props.onSelectedReferral(referral)
                });
            };

            onSelected(client.contactId);
            //NewReferralWizard.popup(client.clientId, onSelected, undefined, undefined, undefined);
        };

        const entries: Array<{id: number, name: string, disabled: boolean}> =
                this.props.sessionData.getListDefinitionEntriesByListName("incident-link-client")
                        .map(entry => ({id: entry.getId(), name: entry.getName(), disabled: entry.getDisabled()}));

        return !this.state.listId
                ? <ClientListWithListControl listDefs={entries} onChange={(listId) => this.setState({listId})}/>
                : <ClientList
                        sessionData={this.props.sessionData}
                        subFormsAsModal={this.props.subFormsAsModal}
                        existingOnly={this.props.existingOnly}
                        onChange={(client, isNew) => onClientChange(this.props.sessionData, client, isNew)}
                        selectedResultRender={false}
                />
    }

}

export class ClientList extends SearchAndImportList<Client, PersonSearchCriteriaDto> {

    public static addClientReferralModal(existingOnly: boolean, onSelectedReferral: (referral: ReferralSummaryDto) => void) {
        const cancelRef = {} as MutableRefObject<() => void>;

        const onNewClient = (sessionData: SessionData, client: Client) => {
            cancelRef.current();
            NewReferralWizard.popup(client.clientId, (referral) => onSelectedReferral(referral))
        };

        const content = withSessionData(sessionData =>
                <ClientList
                    sessionData={sessionData}
                    subFormsAsModal={false}
                    existingOnly={existingOnly}
                    onChange={(client, isNew) => isNew && onNewClient(sessionData, client)}
                    selectedResultRender={true}
                    selectedResultRenderer={client =>
                        <ClientReferralsPopup
                            client={client}
                            existingOnly={existingOnly}
                            onSelectedReferral={(sessionData, referral) => {
                                console.debug("referral selected: %o", referral);
                                cancelRef.current();
                                onSelectedReferral(referral);
                            }
                            }
                        />
                    }
                />
        );
        showReactInModal("add client",
            <ErrorBoundary><AsyncSessionData promiseFn={SessionDataService.getFeatures}><ServicesContextProvider>
                {content}
            </ServicesContextProvider></AsyncSessionData></ErrorBoundary>,
            {
                action: "cancel",
                saveEnabled: false,
                unmountCallbackRef: cancelRef
            });
    }

    private repository = new ClientAjaxRepository(apiClient);

    constructor(props) {
        super(props, PersonSearchBar, 'clientId', 'client');
    }

    protected getNewT(): Client {
        return {discriminator: "client"} as Client;
    }


    protected queryForResults(criteria: PersonSearchCriteriaDto): Promise<StringToObjectMap<DelegateResponse<Client[]>>> {
        return this.repository.findAllClientsByCriteria(criteria);
    }

    protected matchOnExternalRef(client: Client, entry: Client) {
        return entry.externalSystemRef == client.externalSystemRef
            && entry.externalSystemSource == client.externalSystemSource;
    }

    // TODO have any pre-client wizard stuff here
    protected prepareNewResult(criteria: PersonSearchCriteriaDto, client: Client) {
        if (client) {
            client = {...client};
        } else {
            // use what was put in for the search as the initial data
            client = this.getNewT();
            client.firstName = criteria.firstName || null;
            client.lastName = criteria.lastName || null;
        }

        return client;
    }

    protected handleMinimisedNewTClick(clientIn: Client) {
        window.location.href = clientIn
            ? `${environment.baseURI}r/clients/import/${clientIn.externalSystemSource}/${clientIn.externalSystemRef}`
            : `${environment.baseURI}r/referrals/new`;
    }

    protected getDetailSubForm() {
        return <div key='client-detail'>
            <ClientDetailNew
                client={this.state.newRecord}
                sessionData={this.props.sessionData}
                show={this.state.subForm.detail}
                showAsModal={this.props.subFormsAsModal}
                onSave={(client) => this.handleSave(client)}
                onCancel={this.handleSubFormCancel}/>
        </div>;
    }

    protected getResultHeadings(): ReactElement {
        // <> is equiv to <React.Fragment> and results in a React.Fragment entry and results in no entries when generating the DOM nodes
        return <>
            <th>client</th>
            <th>date of birth</th>
            <th>gender</th>
            <th>ni</th>
        </>;
    }
    protected getRemoteResultRenderer(): (result: Client) => ReactElement {
        return client => (
            <tr
                key={client.externalSystemRef}>
                <td>{client.firstName} {client.lastName}</td>
                <td>{client.birthDate}</td>
                <td>{client.genderId ? this.props.sessionData.getListDefinitionEntryById(client.genderId).getDisplayName() : ""}</td>
                <td>{client.ni}</td>
                <td>
                    <div className='pull-right'>
                        {this.alreadyImported(client)
                            ? (<span>imported</span>)
                            : (<Button
                                onClick={() => this.handleNewTClick(client)}
                                bsStyle='default'>
                                import
                            </Button>)
                        }
                    </div>
                </td>
            </tr>
        );
    }

    protected getEccoResultRenderer(): (result: Client) => ReactElement {
        return client => (
            <tr
                onClick={() => this.handleEccoClick(client)}
                key={client.clientId}>
                <td>{this.props.selectedResultRender || !this.props.onChange // onChange is needed by handleEccoClick (so fallback to selectedResultRenderer if not set
                    ? this.props.selectedResultRenderer(client)
                    : (<>
                        <a style={{cursor: "pointer"}}>{client.firstName} {client.lastName}</a>
                        <br/>
                        <small>{client.code || client.clientId.toString()}</small>
                    </>)
                }</td>
                <td>{client.birthDate}</td>
                <td>{client.genderId ? this.props.sessionData.getListDefinitionEntryById(client.genderId).getDisplayName() : ""}</td>
                <td>{client.ni}</td>
            </tr>
        );
    }
}
