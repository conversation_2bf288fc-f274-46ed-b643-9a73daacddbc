import $ = require("jquery");
import {getFormEvidenceRepository, getFeatureConfigRepository} from "ecco-offline-data";
import MvcUtils from "../mvc/MvcUtils";
import URI = require("URI");
import {RiskStatusAreaControl} from "../evidence/risk/RiskStatusAreaControl";
import {ReferralContactsEnhance} from "../referral/ReferralContacts";
import * as React from "react";
import {CustomFormWithSignatureForm} from "../referral/components/signedCustomForm";
import {EvidenceGroup} from "ecco-dto";
import {getGlobalEccoAPI, loadSrWithEntitiesPromise} from "ecco-components";
import {reactElementContainer} from "ecco-components-core";
import {EmergencyDetails} from "../referral/components/EmergencyDetailsForm";
import {ifNeededSetDefaultGlobalEccoAPI} from "../ifNeededSetDefaultGlobalEccoAPI";

export class GrabSheetPage {

    //noinspection JSUnusedLocalSymbols - Suppressed as we use singleton to instantiate
    /** Singleton which kicks off attach() once only */
    private static instance = new GrabSheetPage();

    constructor() {
        $( () => {
            this.attach();
        });
    }

    private attach() {

        const $riskFlags = $("<div>");
        const $customForm = $("<div>");
        const $emergencyDetails = $("<div>");
        const $contacts = $("<div>");

        const $content = $("#main-content");
        $content.append($riskFlags);
        $content.append($customForm);
        $content.append($emergencyDetails);
        $content.append($contacts);

        // expect /service-recipients/{srId}/grabSheet
        let uri = URI(window.location.href);
        const pathParts = MvcUtils.getAppPathComponents(uri);
        const srId = parseInt(pathParts[pathParts.length - 3]); // end part is always empty ""

        getFeatureConfigRepository().getSessionData().then(sessionData => {
            ifNeededSetDefaultGlobalEccoAPI(sessionData);
            return loadSrWithEntitiesPromise(srId, getGlobalEccoAPI()).then( svcRec => {

                const flags = sessionData.getSetting("referral.emergency.riskFlags");

                // look for any matching config (could be different risk pages etc)
                // see also ServiceRecipientAjaxRepository#getFlagEvidencePagesForStatusArea
                const flagForGrabSheet = svcRec.serviceType
                            .getTaskDefinitionEntries()
                            .filter(td => td.hasSetting("showFlagsOn", "emergencySheet"))
                            .filter(td => td.hasSetting("showFlags", "y"));

                if (flags || flagForGrabSheet) {
                    // grabsheet uses the same control as StatusPanel
                    const ctl = new RiskStatusAreaControl(srId);
                    $riskFlags.append(ctl.element());
                    ctl.load();
                }

                const st = sessionData.getServiceTypeByServiceCategorisationId(svcRec.serviceRecipient.serviceAllocationId);
                const emergencyFormDef = st.getTaskDefinitionSetting("emergencyDetails", "formDefinition");
                if (emergencyFormDef) {
                    getFormEvidenceRepository().findLatestFormEvidenceSnapshotByServiceRecipientId(srId, EvidenceGroup.emergencyDetails).then(evd => {
                        CustomFormWithSignatureForm.enhanceForPrinting($customForm, () => {
                        }, srId, "emergencyDetails", "emergencyDetails", null, evd?.id, false, true);
                    })
                }

                const emergencyFields = <EmergencyDetails
                    srId={srId}
                    taskHandle={null} // NB task could be filtered from context.workflowActiveRecord.getTasks() filter by name (see TaskList.tsx for the source)
                    client={svcRec.client || svcRec.worker}
                    sessionData={sessionData}
                    serviceType={svcRec.serviceType}
                    commandForm={undefined}
                    readOnly={true}
                    displayOnlyPopulatedFields={!!emergencyFormDef}
                    />
                reactElementContainer(emergencyFields, $emergencyDetails[0]);

                ReferralContactsEnhance($contacts, srId, true);
                //const form = <ReferralContacts serviceRecipientId={srId} sessionData={sessionData} printView={false}/>;
                //reactElementContainer(form, $contacts[0]);
                //ReferralAssociatedContactControl.enhance()
            })
        });
    }

}
