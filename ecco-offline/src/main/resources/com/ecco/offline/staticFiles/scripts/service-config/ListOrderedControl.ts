import $ = require("jquery");

import BaseAsyncCommandForm from "../cmd-queue/BaseAsyncCommandForm";
import BaseControl from "../controls/BaseControl";
import * as events from "../common/events";
import {AdminMode, bus, SelectListOption} from "@eccosolutions/ecco-common";
import TextInput from "../controls/TextInput";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {BaseUpdateCommand} from "ecco-commands";
import {adminModeEnabled, NumberChangeOptional} from "ecco-dto";
import CheckboxInput from "../controls/CheckboxInput";
import SelectList from "../controls/SelectList";


class ChangeCommandEvent {
    constructor(public cmd: ListEntryCommand) {
    }
}
const changeCommandEvents = bus<ChangeCommandEvent>();


export interface EntryRow {
    getUuid(): string;
    getEntryDto(): ListEntryDto;
    getCells(): EntryCell<any>[];
    getCellsWidth(): number[];
    getOrderBy(): EntryCellNumber
    getCommand(): ListEntryCommand;
    getEntryRowsChildren(): EntryRow[];
    createEntryRowChild(orderBy: number): EntryRow;
    createEntryRowChildText(): string;
}

export interface EntryCell<T> {
    render(adminMode: boolean): $.JQuery;
    getValue(): T
    setValue(val: T);
    getWidth(): number;
}

export abstract class EntryRowBase implements EntryRow {

    public abstract getUuid(): string;
    public abstract getEntryDto(): ListEntryDto;
    public abstract getCells(): EntryCell<any>[];
    public abstract getCellsWidth(): number[];
    public abstract getOrderBy(): EntryCellNumber
    public abstract getEntryRowsChildren(): EntryRow[];
    public abstract getCommand(): ListEntryCommand;
    public abstract createEntryRowChild(orderBy: number): EntryRow;
    public abstract createEntryRowChildText(): string;

    protected triggerChangeEvent() {
        changeCommandEvents.fire( new ChangeCommandEvent(this.getCommand()) );
    }

}

export abstract class EntryRowNameOrderDisabled extends EntryRowBase {
    protected name: EntryCellString;
    protected orderBy: EntryCellNumber;
    protected id: EntryCellId;
    protected disabled: EntryCellBoolean;

    protected constructor(protected listEntryDto: ListEntryDto, protected cmd: ListEntryCommand) {
        super();
        this.name = new EntryCellString(listEntryDto.name, (value) => this.changedName(value));
        this.orderBy = new EntryCellNumber(listEntryDto.orderby, (value) => this.changedOrderby(value));
        this.disabled = new EntryCellDisabled(listEntryDto.disabled, (value) => this.changedDisabled(value));
        this.id = new EntryCellId(listEntryDto.id);
    }

    public abstract override getEntryRowsChildren(): EntryRow[];
    public abstract override createEntryRowChild(orderBy: number): EntryRow;
    public abstract override createEntryRowChildText(): string;

    public getUuid(): string {
        return this.listEntryDto.uuid;
    }

    public getEntryDto(): ListEntryDto {
        return this.listEntryDto;
    }

    public getCells(): EntryCell<any>[] {
        return [this.name, this.orderBy, this.disabled, this.id];
    }
    public getCellsWidth(): number[] {
        return [this.name.getWidth(), this.orderBy.getWidth(), this.disabled.getWidth(), this.id.getWidth()];
    }

    public getOrderBy(): EntryCellNumber {
        return this.orderBy;
    }

    public getCommand(): ListEntryCommand {
        return this.cmd;
    }

    private changedName(name: string) {
        this.cmd.changeName(this.listEntryDto.name, name);
        this.triggerChangeEvent();
    }
    private changedOrderby(orderby: number) {
        this.cmd.changeOrderBy(this.listEntryDto.orderby, orderby);
        this.triggerChangeEvent();
    }
    private changedDisabled(disabled: boolean) {
        this.cmd.changeDisabled(this.listEntryDto.disabled, disabled);
        this.triggerChangeEvent();
    }

}

export class EntryCellBoolean implements EntryCell<boolean> {
    constructor(private value: boolean, private change: (value: boolean) => void, private width = 2) {}

    public render(adminMode: boolean): $.JQuery {
        if (!adminMode) {
            return $("<span>").text(this.getDisplay(this.value));
        } else {
            const ti = new CheckboxInput("");
            ti.setChecked(this.value);
            ti.change((val, checked) => this.changedValue(checked));
            return ti.element().addClass("form-control");
        }
    }

    protected getDisplay(value: boolean): string {
        return value ? "on" : "off"
    }

    public getValue(): boolean {
        return this.value;
    }
    public setValue(val: boolean) {
    }
    public getWidth(): number {return this.width;}
    private changedValue(valueNew: boolean) {
        this.value = valueNew;
        this.change(valueNew);
    }
}

export class EntryCellDisabled extends EntryCellBoolean {
    protected override getDisplay(value: boolean): string {
        return value ? "disabled" : ""
    }
}

export class EntryCellString implements EntryCell<string> {
    constructor(private value: string, private change: (value: string) => void, private width = 6) {}

    public render(adminMode: boolean): $.JQuery {
        if (!adminMode) {
            return $("<span>").text(this.value);
        } else {
            const ti = new TextInput("");
            ti.setVal(this.value);
            ti.change((val) => this.changedValue(val));
            return ti.element().addClass("form-control");
        }
    }
    public getValue(): string {
        return this.value;
    }
    public setValue(val: string) {
    }
    public getWidth(): number {return this.width;}
    private changedValue(valueNew: string) {
        this.value = valueNew;
        this.change(valueNew);
    }
}

export class EntryCellNumber implements EntryCell<number> {
    private input: TextInput;

    constructor(private value: number, private change: (value: number) => void, private width = 2) {}

    public render(adminMode: boolean): $.JQuery {
        if (!adminMode) {
            return $("<span>").text(this.value);
        } else {
            this.input = new TextInput("");
            this.input.setVal(this.value ? this.value.toString() : undefined);
            this.input.change((val) => this.changedValue(val == "" ? this.value : parseInt(val)));
            return this.input.element().addClass("form-control");
        }
    }
    public getValue(): number {
        return this.value;
    }
    public setValue(val: number) {
        this.input.setVal(val.toString());
    }
    public getWidth(): number {return this.width;}
    private changedValue(valueNew: number) {
        this.value = valueNew;
        this.change(valueNew);
    }
}

export class EntryCellId implements EntryCell<number> {
    constructor(private value: number, private width = 1) {}

    public render(adminMode: boolean): $.JQuery {
        return $("<span>").text(this.value);
    }
    public getValue(): number {
        return this.value;
    }
    public setValue(val: number) {
    }
    public getWidth(): number {return this.width;}
}

export class EntryCellDropdown implements EntryCell<number> {

    private ti: SelectList;

    constructor(private id: number, private change: (id: number) => void,
                private name: string, private values: SelectListOption[], private disabled: boolean) {}

    public render(adminMode: boolean): $.JQuery {
        if (!adminMode) {
            const valueText = this.id ? this.values.find(v => v.id == this.id).name : "";
            return $("<span>").text(valueText);
        } else {
            //const ti = createDropdownList(this.name, this.name, "", this.value, (val) => this.changedValue(val.toString()), this.values);
            this.ti = new SelectList(this.name, undefined, undefined, undefined, this.disabled);
            this.ti.change(v => this.changedValue(parseInt(v)));
            this.ti.populateFromList(this.values, (v) => ({
                key: v.id.toString(),
                value: v.name
            }), (item => item.id == this.id));
            return this.ti.element().addClass("form-control");
        }
    }
    public getValue(): number {
        return this.ti ? parseInt(this.ti.getValueOrNull(!!this.id)) : this.id;
    }
    public setValue(val: number) {
    }
    public getWidth(): number {return 3;}
    private changedValue(valueNew: number) {
        //this.ti.setVal(valueNew.toString());
        if (this.id != valueNew) {
            this.change(valueNew);
        }
    }
}

export interface ListEntryDto {
    id: number;
    uuid?: string;
    parentDefUuid?: string;
    name: string;
    disabled: boolean;
    orderby: number;
    children: ListEntryDto[];
    type?: string; // allow discriminator between what children are
}

export interface ListEntryCommand {
    getUuid(): Uuid;
    getOrderByChange(): NumberChangeOptional;
    changeName(from: string, to: string);
    changeOrderBy(from: number, to: number);
    changeDisabled(from: boolean, to: boolean);
    commandName: string;
}

class ListEntryOrderedControl extends BaseControl {
    private $valueArea: $.JQuery;

    constructor(private entryRow: EntryRow,
                private countSiblingsBelowOrder: (order: number) => number) {

        super( $("<li>") );
        const iconClass = "fa fa-cog";

        this.$valueArea = $("<span>");

        this.renderValues(adminModeEnabled());

        //this.element().hide();

        this.element().addClass("row")
            .append(
                $("<div>").addClass("col-xs-1").append(
                    $("<span>").addClass("fa fa-lg").addClass(iconClass)
                )
            )
            .append(this.$valueArea);
    }

    public setAdminMode(enabled: boolean) {
        this.renderValues(enabled);
    }

    public getOrderBy() {
        return this.entryRow.getOrderBy().getValue();
    }

    public getCommand(): ListEntryCommand {
        return this.entryRow.getCommand();
    }

    public setOrderbyField(orderby: number) {
        this.entryRow.getOrderBy().setValue(orderby);
    }

    private renderValues(adminMode: boolean) {
        this.$valueArea.empty();

        this.entryRow.getCells().forEach((c, i) => {
            this.$valueArea.append($("<div>").addClass("col-xs-"+this.entryRow.getCellsWidth()[i]).append(c.render(adminMode)));
        });
    }

    public indicateAsUnchanged() {
        this.$valueArea.find(":input").css("border-color", "");
    }

    public indicateAsChanged() {
        this.$valueArea.find(":input").css("border-color", "gold");
    }

    public renderTransientCommand(cmdChanged: ListEntryCommand) {
        this.indicateAsChanged();

        // isActionGroupCmd = (<ActionGroupDefChangeCommand>cmd).commandName == ActionGroupDefChangeCommand.discriminator
        // isActionCmd = (<ActionDefChangeCommand>cmd).commandName == ActionDefChangeCommand.discriminator
        const cmdAs = cmdChanged as any as BaseUpdateCommand;
        if (cmdAs.hasChanges() != null) {
            // if we haven't yet implemented the ordering... ignore it
            if (cmdChanged.getOrderByChange() != null) {
                const newOrderby = cmdChanged.getOrderByChange().to;
                const newIndex = this.countSiblingsBelowOrder(newOrderby) - 1;
                const ctrlRow = this.element();
                const callback = () => {
                    if (newIndex < 0) {
                        ctrlRow.insertBefore(ctrlRow.siblings(':eq(0)'));
                    } else {
                        ctrlRow.insertAfter(ctrlRow.siblings(':eq(' + newIndex + ')'));
                    }
                };
                ctrlRow.slideUp(250, callback).slideDown(250);
            }
        }
    }

}

class ParentWithChildrenControl extends ListEntryOrderedControl {

    private controlsChildren: ListEntryOrderedControl[] = [];
    //private $grp = $("<span>");//$("<ul>").addClass("entry-list list-unstyled");
    private $children = $("<div>").css("clear", "both");//$("<ul>").addClass("entry-list list-unstyled");
    private $newChildEntry = $("<li>").addClass("add-entry").css({"text-align": "center", "color": "#777"});

     constructor(private parent: EntryRow,
                countSiblingsBelowOrder: (order: number) => number) {
        super(parent, countSiblingsBelowOrder);
        //super(actionGroup, "fa fa-cogs");
        this.renderChildren();
    }

    protected renderChildren() {
        //this.element().show();
        const children = this.parent.getEntryRowsChildren();
        children.map(a => {
            const ctl = new ListEntryOrderedControl(a, (o: number) => this.countActionSiblingsBelowOrder(o));
            this.controlsChildren.push(ctl);
            this.$children.append(ctl.element());
            ctl.element().show();
            return ctl;
        });
        this.element().append(this.$children);

        this.renderNewChildButton(this.parent);
        this.element().append(this.$newChildEntry);
    }

    protected getEntryIconClass(): string {
        return "fa fa-cogs";
    }

    public override setAdminMode(adminMode: boolean) {
        super.setAdminMode(adminMode);
        if (adminMode) {
            this.$newChildEntry.show();
        } else {
            this.$newChildEntry.hide();
        }
        this.controlsChildren && this.controlsChildren.forEach( (item) => item.setAdminMode(adminMode) );
    }

    public getCommands(): ListEntryCommand[] {
        return [this.getCommand()].concat(this.controlsChildren.map(c => c.getCommand()));
    }

    public handleTransientCommand(cmdChanged: ListEntryCommand) {
        if (cmdChanged.getUuid() != this.getCommand().getUuid()) {
            const aCtl = this.controlsChildren.filter(c => c.getCommand().getUuid() == cmdChanged.getUuid())[0];
            if (aCtl) {
                aCtl.renderTransientCommand(cmdChanged);
            }
        } else {
            super.renderTransientCommand(cmdChanged);
        }
    }

    public override indicateAsUnchanged() {
        super.indicateAsUnchanged();
        this.controlsChildren.every(c => c.indicateAsUnchanged());
    }

    private countActionSiblingsBelowOrder(order: number): number {
        return this.controlsChildren
            .filter(ctrl => ctrl.getOrderBy() < order)
            .length;
    }

    private renderNewChildButton(parent: EntryRow) {
         if (parent.createEntryRowChildText()) {
            this.$newChildEntry
                .append($("<i>").addClass("fa fa-file-code-o"))
                .append( $("<span>").text(" " + parent.createEntryRowChildText()))
                .click( () => {
                    const maxOrderBy = this.controlsChildren.length == 0 ? 5 : this.controlsChildren.reduce((prev, curr) => curr.getOrderBy() > prev.getOrderBy() ? curr : prev).getOrderBy();
                    const ctl = new ListEntryOrderedControl(parent.createEntryRowChild(maxOrderBy + 5), (o: number) => this.countActionSiblingsBelowOrder(o));
                    this.controlsChildren.push(ctl);
                    this.$children.append(ctl.element());
                });
        }
    }

}
interface IdName {
    id: number;
    uuid: string;
    name: string;
}

export abstract class ListOrderedControl<T extends IdName> extends BaseAsyncCommandForm<T> {
    private controls: ParentWithChildrenControl[];
    private footer: $.JQuery;
    private $groups: $.JQuery;
    private $newEntry: $.JQuery;

    protected constructor() {
        super("-not used-");
        //OfflineSyncStatusEvent.bus.addHandler(saved => this.resetCommands());
        this.setOnFinished( () => {
            this.resetCommands();
            // delay(2000).then( () => {
            //     window.location.reload(); // will show "install"
            // });
        } );
        changeCommandEvents.addHandler((ev: ChangeCommandEvent) => this.handleChangeCommandEvent(ev.cmd));
    }

    public abstract getParentEntryRows(data: T): EntryRow[];
    public abstract getNewParentEntryRow(data: T): EntryRow;
    public abstract getNewParentEntryRowText(): string;
    public abstract getManageEntityText(): string;

    protected render(data: T): void {
        this.element().empty();
        this.controls = [];

        this.$newEntry = $("<li>").addClass("add-entry");
        this.$groups = $("<ul>").addClass("entry-list list-unstyled");

        this.renderNewParentButton(data);

        const entryRows = this.getParentEntryRows(data);
        this.controls = entryRows.map(er => {
            const ctl = new ParentWithChildrenControl(er, (o: number) => this.countParentSiblingsBelowOrder(o));
            ctl.element().show();
            return ctl;
        });
        this.$groups.append( this.controls.map( (control) => control.element() ) );
        if (this.controls.length == 0) {
            this.$groups.append($("<li>").text("no groups available"));
        }

        this.element().append($("<ul>").addClass("entry-list list-unstyled").append(this.$newEntry));
        this.element().append(this.$groups);

        this.footer = this.getFooter();
        var $nav = $("<div>").addClass("clearfix")
            .append("manage " + this.getManageEntityText() + " for: " + data.id + " [" + data.name + "]", this.footer);
        events.MenuUpdateEvent.bus.fire( new events.MenuUpdateEvent("nav", $nav) );

        this.setAdminMode(adminModeEnabled());

        AdminMode.bus.addHandler(event => {
            this.setAdminMode(event.enabled);
        });
    }

    public createNewParentWithChildrenControl(data: T) {
        const ctl = new ParentWithChildrenControl(this.getNewParentEntryRow(data), (o: number) => this.countParentSiblingsBelowOrder(o));
        ctl.element().show();
        return ctl;
    }

    private handleChangeCommandEvent(cmdChange: ListEntryCommand) {
        this.commandQueue.clear();

        this.controls.map(grpCtl => {
            grpCtl.getCommands().forEach(c => {
                const cmdAs = c as any as BaseUpdateCommand;
                cmdAs.hasChanges() && this.commandQueue.addCommand(cmdAs);
            });
            grpCtl.handleTransientCommand(cmdChange);
        });
        if (this.commandQueue.size() > 0) {
            this.enableSubmit();
        } else {
            this.disableSubmit();
            this.controls && this.controls.forEach(item => item.indicateAsUnchanged() );
        }
    }

    public resetCommands() {
        this.load();
    }

    private setAdminMode(adminMode: boolean) {
        if (adminMode) {
            this.$newEntry.show();
            this.footer.show();
        } else {
            this.$newEntry.hide();
            this.footer.hide();
        }
        this.controls && this.controls.forEach( (item) => item.setAdminMode(adminMode) );
    }

    private renderNewParentButton(data: T) {
        this.$newEntry
            .append( $("<i>").addClass("fa fa-file-code-o") )
            .append( $("<span>").text(" " + this.getNewParentEntryRowText()) )
            .hide()
            .click( () => {
                const maxOrderBy = this.controls.length == 0 ? 5 : this.controls.reduce((prev, curr) => curr.getOrderBy() > prev.getOrderBy() ? curr : prev).getOrderBy();
                const ctl = this.createNewParentWithChildrenControl(data);
                this.controls.push(ctl);
                this.$groups.append(ctl.element());
                ctl.setAdminMode(true); // we must be in admin mode to add

                ctl.setOrderbyField(maxOrderBy + 5);
            });
    }

    private countParentSiblingsBelowOrder(order: number): number {
        return this.controls
            .filter(ctrl => ctrl.getOrderBy() < order)
            .length;
    }

}

