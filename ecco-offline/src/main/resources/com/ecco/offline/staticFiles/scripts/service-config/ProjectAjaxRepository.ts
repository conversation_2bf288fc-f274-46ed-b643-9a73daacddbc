import {SparseArray} from "@eccosolutions/ecco-common";
import {ApiClient, ProjectDto} from "ecco-dto";
import {ProjectRepository} from "./ProjectRepository";

export class ProjectAjaxRepository implements ProjectRepository {

    private cache: SparseArray<Promise<ProjectDto>> = {};

    constructor(private apiClient: ApiClient) {
    }

    public findAll(): Promise<ProjectDto[]> {
        return this.apiClient.get<ProjectDto[]>("project/");
    }

    public findOneProject(projectId: number): Promise<ProjectDto> {
        var project = this.cache[projectId];
        if (!project) {
            project = this.apiClient.get<ProjectDto>(`project/${projectId}/`);
            this.cache[projectId] = project;
        }
        return project;
    }
}
