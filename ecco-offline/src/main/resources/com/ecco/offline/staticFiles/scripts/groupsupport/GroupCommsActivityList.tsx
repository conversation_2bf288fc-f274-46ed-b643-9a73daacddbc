import * as React from "react";
import {FC, useMemo} from "react";
import {GroupActivityList, GroupActivityOptions} from "./GroupActivityList";
import GroupCommsActivityListControl from "./GroupCommsActivityListControl";
import {GroupActivityCommand} from "./commands";
import {GroupPageType} from "ecco-dto";

export const groupCommsActivityOptions: GroupActivityOptions = {
    discriminator_orm: GroupActivityCommand.DISCRIMINATOR_COMMS,
    title: "group comms",
    hasActivityType: true,
    hasVenue: false,
    hasCapacity: false,
    hasDuration: false,
    allowAttended: false,
    addService: true,
    hasCategory: false,
    hasReviewDate: false,
    activityListName: "TODO",
    createListControl: (groupPageType: GroupPageType, venueId: number, serviceId: number) => new GroupCommsActivityListControl(groupPageType, venueId, serviceId)
}

/** entry point to the groups */
export const GroupCommsActivityList: FC = () => {
    const options = useMemo(() => groupCommsActivityOptions, []);
    return <GroupActivityList options={options}/>;
}
