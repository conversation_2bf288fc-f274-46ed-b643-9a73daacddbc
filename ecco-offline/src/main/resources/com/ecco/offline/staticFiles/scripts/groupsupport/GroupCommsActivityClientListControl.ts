import {GroupActivityClientListControl, GroupActivityClientRowControl} from "./GroupActivityClientListControl";
import {ClientAttendanceDto, GroupActivityDto, SessionData} from "ecco-dto";
import {GroupActivityOptions} from "./GroupActivityList";

export class GroupCommsActivityClientListControl extends GroupActivityClientListControl {

    protected getHeaders() {
        let headings = ["client", "register", "scheduled", "sent", "status"];
        //headings.push("support note");
        return headings;
    }

    protected createRow(clientAttendance: ClientAttendanceDto) {
        return new CommsClientRowControl(this.sessionData, this.activity, this.options, clientAttendance, this.printable, () => this.$saveButton.prop("disabled", false));
    }
}


class CommsClientRowControl extends GroupActivityClientRowControl {

    constructor(sessionData: SessionData, activity: GroupActivityDto, options: GroupActivityOptions,
                clientAttendance: ClientAttendanceDto, printable: boolean,
                onChangeCallback: () => void) {
        super(sessionData, activity, options, clientAttendance, printable, onChangeCallback);
    }

}
