import $ = require("jquery");

import BaseAsyncListControl from "../controls/BaseAsyncListControl";
import BaseListEntryControl from "../controls/BaseListEntryControl";
import EditProjectForm from "./EditProjectForm";
import {apiClient} from "ecco-components";
import {ProjectDto, SessionDataGlobal} from "ecco-dto";
import {ProjectAjaxRepository} from "./ProjectAjaxRepository";

var repository = new ProjectAjaxRepository(apiClient);


class EntryControl extends BaseListEntryControl<ProjectDto> {

    constructor(project: ProjectDto) {
        super(project, "fa fa-pencil");
    }

    protected administerEntry(): void {
        EditProjectForm.showInModal(this.entry.id);
    }

    protected getEditElement(): $.JQuery {
        const $ids = $("<small>").text(`[id:${this.entry.id}]  `);
        const $name = $("<span>").text(this.entry.name);
        const $row = $("<div>").addClass("row")
                .append($("<div>").addClass("col-xs-8").append($name))
                .append($("<div>").addClass("col-xs-4").append($ids))
        return $("<span>").append($row);
    }

    protected getEntryIconClass(): string {
        return "fa fa-home";
    }
}

class ProjectsListControl extends BaseAsyncListControl<ProjectDto> {

    constructor() {
        super("add new project", "no projects defined", "fa fa-home");
    }

    protected fetchViewData(): Promise<ProjectDto[]> {
        return repository.findAll().then(list => list.sort(SessionDataGlobal.compare));
    }
    protected createItemControl(project: ProjectDto) {
        return new EntryControl(project);
    }

    protected addNewEntity() {
        EditProjectForm.showInModal(null);
    };

}
export default ProjectsListControl;
