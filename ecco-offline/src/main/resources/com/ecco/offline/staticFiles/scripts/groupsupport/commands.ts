import {
    BooleanChange,
    Mergeable,
    <PERSON><PERSON><PERSON>eOptional,
    StringChange,
    StringChangeOptional,
    UpdateCommandDto
} from "ecco-dto";
import * as commands from "ecco-commands";
import {EccoDate, EccoDateTime} from "@eccosolutions/ecco-common";
import {Uuid} from "@eccosolutions/ecco-crypto";


export interface GroupSupportTypeCommandDto extends UpdateCommandDto {
    operation: string;
    id: number;
    name: StringChange;
    disabled: BooleanChange;
}

export class GroupSupportTypeCommand extends commands.BaseUpdateCommand {
    nameChange: StringChange;
    disabledChange: BooleanChange;

    constructor(private operation: string, private id: number) {
        super("activities/commands/type/");
    }

    public override canMerge(candidate: Mergeable) {
        // TODO check instanceof due to 'adapt' in CommandQueue.addCommand
        if (!(candidate instanceof GroupSupportTypeCommand)) {
            return false;
        }

        var otherCommand = <GroupSupportTypeCommand>candidate;

        return this.getCommandTargetUri() == otherCommand.getCommandTargetUri()
                && this.id == otherCommand.id
    }

    public override merge(previousCommand: this): this {
        // keep the last command only if it will make an update
        if (this.nameChange != null && this.nameChange.from !== this.nameChange.to) return this;
        if (this.disabledChange != null && this.disabledChange.from !== this.disabledChange.to) return this;
        return null;
    }

    public changeDisabled(from: boolean, to: boolean) {
        this.disabledChange = this.asBooleanChange(from, to);
        return this;
    }

    public changeName(from: string, to: string) {
        this.nameChange = this.asStringChange(from, to);
        return this;
    }

    public toDto(): GroupSupportTypeCommandDto {
        return ({
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            operation: this.operation,
            id: this.id,
            name: this.nameChange,
            disabled: this.disabledChange
        });
    }
}


export interface GroupActivityInvitationCommandDto extends UpdateCommandDto {
    operation: string;
    activityId: number; // TODO: Remove this and use activityUuid for operations
    activityUuid: string;
    referralId: number;
    invited: BooleanChange;
    attending: BooleanChange;
    attended: BooleanChange;
    evidenceNotes: StringChangeOptional;
    evidenceType: NumberChangeOptional;
}

/**
 * Command to add or remove an assocation between a referral and a group support activity.
 */
export class GroupActivityInvitationCommand extends commands.BaseUpdateCommand {
    invitedChange: BooleanChange;
    attendingChange: BooleanChange;
    attendedChange: BooleanChange;
    evidenceNotesChange: StringChangeOptional;
    evidenceTypeChange: NumberChangeOptional;

    constructor(private operation: string, private activityId: number, private activityUuid: Uuid,
            private referralId: number) {
        super("activities/commands/invitation/");
    }

    public override canMerge(candidate: Mergeable) {
        // TODO check instanceof due to 'adapt' in CommandQueue.addCommand
        if (!(candidate instanceof GroupActivityInvitationCommand)) {
            return false;
        }

        var otherCommand = <GroupActivityInvitationCommand>candidate;

        return this.getCommandTargetUri() == otherCommand.getCommandTargetUri()
            && this.activityId == otherCommand.activityId
            && this.referralId == otherCommand.referralId;
    }

    public override merge(previousCommand: this): this {
        // keep the last command only if it will make an update
        if (this.invitedChange != null && this.invitedChange.from !== this.invitedChange.to) return this;
        if (this.attendingChange != null && this.attendingChange.from !== this.attendingChange.to) return this;
        if (this.attendedChange != null && this.attendedChange.from !== this.attendedChange.to) return this;
        if (this.evidenceNotesChange != null && this.evidenceNotesChange.from !== this.evidenceNotesChange.to) return this;
        if (this.evidenceTypeChange != null && this.evidenceTypeChange.from !== this.evidenceTypeChange.to) return this;
        return null;
    }

    public changeInvited(from: boolean, to: boolean) {
        this.invitedChange = this.asBooleanChange(from, to);
        return this;
    }

    public changeAttending(from: boolean, to: boolean) {
        this.attendingChange = this.asBooleanChange(from, to);
        return this;
    }

    public changeAttended(from: boolean, to: boolean) {
        this.attendedChange = this.asBooleanChange(from, to);
        return this;
    }

    public changeEvidenceNotes(from: string, to: string) {
        this.evidenceNotesChange = this.asStringChange(from, to);
        return this;
    }

    public changeEvidenceType(from: number, to: number) {
        this.evidenceTypeChange = this.asNumberChange(from, to);
        return this;
    }

    public toDto(): GroupActivityInvitationCommandDto {
        return ({
                uuid: this.getUuid().toString(),
                commandUri: this.commandUri,
                timestamp: this.timestamp,
                operation: this.operation,
                activityId: this.activityId,
                activityUuid: this.activityUuid.toString(),
                referralId: this.referralId,
                invited: this.invitedChange,
                attending: this.attendingChange,
                attended: this.attendedChange,
                evidenceNotes: this.evidenceNotesChange,
                evidenceType: this.evidenceTypeChange
            });
    }
}

export interface GroupActivityCommandDto extends UpdateCommandDto {
    operation: string;
    activityUuid: string;
    discriminator_orm: string;
    parentId: number;
    course: boolean;
    description: StringChangeOptional;
    serviceId: NumberChangeOptional;
    projectId: NumberChangeOptional;
    activityTypeId: NumberChangeOptional;
    venueId: NumberChangeOptional;
    capacity: NumberChangeOptional;
    duration: NumberChangeOptional;
    startDateTime: StringChangeOptional;
    endDate: StringChangeOptional;
}

export class GroupActivityCommand extends commands.BaseUpdateCommand {
    // see GroupSupportActivity.java
    public static DISCRIMINATOR_COMMS = "comm";
    public static DISCRIMINATOR_SUPPORT = "supp";
    public static DISCRIMINATOR_AUX = "aux";
    public static discriminator = "groupsupportactivity";

    private descriptionChange: StringChangeOptional;
    private serviceChange: NumberChangeOptional;
    private projectChange: NumberChangeOptional;
    private activityTypeChange: NumberChangeOptional;
    private venueChange: NumberChangeOptional;
    private capacityChange: NumberChangeOptional;
    private durationChange: NumberChangeOptional;
    private startDateTimeChange: StringChangeOptional;
    private endDateChange: StringChangeOptional;
    private course: boolean;

    constructor(private operation: string, private activityUuid: Uuid, private discriminator_orm?: string | undefined, private parentId?: number | undefined) {
        super("activities/commands/");
    }

    public override canMerge(candidate: Mergeable) {
        return false;
    }

    public override merge(previousCommand: this): this {
        return null;
    }

    public asCourse() {
        this.course = true;
        return this;
    }

    public changeDescription(from: string, to: string) {
        this.descriptionChange = this.asStringChange(from, to);
        return this;
    }

    public changeService(from: number, to: number) {
        this.serviceChange = this.asNumberChange(from, to);
        return this;
    }

    public changeProject(from: number, to: number) {
        this.projectChange = this.asNumberChange(from, to);
        return this;
    }

    public changeActivityType(from: number, to: number) {
        this.activityTypeChange = this.asNumberChange(from, to);
        return this;
    }

    public changeVenue(from: number, to: number) {
        this.venueChange = this.asNumberChange(from, to);
        return this;
    }

    public changeCapacity(from: number, to: number) {
        this.capacityChange = this.asNumberChange(from, to);
        return this;
    }


    public changeDuration(from: number, to: number) {
        this.durationChange = this.asNumberChange(from, to);
        return this;
    }

    /** Use ISO value but without timezone info which is not relevant */
    public changeStartDateTimeIso(from: string, to: string) {
        // Trim off any +01:00 stuff
        const localTo = to && to.substr(0,16)
        const localFrom = from && from.substr(0,16)
        this.startDateTimeChange = this.asStringChange(localFrom, localTo);
        return this;
    }

    public changeStartDateTime(from: EccoDateTime, to: EccoDateTime) {
        this.startDateTimeChange = this.asLocalDateTimeChange(from, to);
        return this;
    }

    public changeEndDate(from: EccoDate, to: EccoDate) {
        this.endDateChange = this.asDateChange(from, to);
        return this;
    }

    /** We can build this command by throwing possibly 'non-change' data at it.  This will return 'true' only
     *  if the result was that there are changes to submit */
    public override hasChanges(): boolean {
        return this.descriptionChange != null || this.serviceChange != null
            || this.projectChange != null || this.activityTypeChange != null || this.venueChange != null
            || this.capacityChange != null || this.durationChange != null || this.startDateTimeChange != null
            || this.endDateChange != null;
    }

    public toDto(): GroupActivityCommandDto {
        return ({
                    uuid: this.getUuid().toString(),
                    commandName: GroupActivityCommand.discriminator,
                    commandUri: this.commandUri,
                    timestamp: this.timestamp,
                    operation: this.operation,
                    parentId: this.parentId,
                    course: this.course,
                    activityUuid: this.activityUuid.toString(),
                    discriminator_orm: this.discriminator_orm,
                    description: this.descriptionChange,
                    venueId: this.venueChange,
                    serviceId: this.serviceChange,
                    projectId: this.projectChange,
                    activityTypeId: this.activityTypeChange,
                    capacity: this.capacityChange,
                    duration: this.durationChange,
                    startDateTime: this.startDateTimeChange,
                    endDate: this.endDateChange
        });
    }
}

export interface GroupActivityAttendanceCommandDto extends UpdateCommandDto {
    operation: string;
    activityUuid: string;
    activityId: number;
    referralId: number;
    attendedAt: string;
    cancelled: BooleanChange;
    attendedAllDay: BooleanChange;
}

export class GroupActivityAttendanceCommand extends commands.BaseUpdateCommand {

    private cancelledChange: BooleanChange;
    private attendedAllDayChange: BooleanChange;

    constructor(private operation: string, private activityUuid: Uuid,
            private activityId: number, private referralId: number, private attendedAt: EccoDate) {
        super("activities/commands/attendance/");
    }

    public override canMerge(candidate: Mergeable) {
        // TODO check instanceof due to 'adapt' in CommandQueue.addCommand
        if (!(candidate instanceof GroupActivityAttendanceCommand)) {
            return false;
        }

        var otherCommand = <GroupActivityAttendanceCommand>candidate;

        return this.getCommandTargetUri() == otherCommand.getCommandTargetUri()
            && this.activityId == otherCommand.activityId
            && this.referralId == otherCommand.referralId
            && this.attendedAt.equals(otherCommand.attendedAt);
    }

    public override merge(previousCommand: this): this {
        // keep the last command only if it will make an update
        if (this.attendedAllDayChange != null && this.attendedAllDayChange.from !== this.attendedAllDayChange.to) return this;
        if (this.cancelledChange != null && this.cancelledChange.from !== this.cancelledChange.to) return this;
        return null;
    }

    public changeCancelled(from: boolean, to: boolean) {
        this.cancelledChange = this.asBooleanChange(from, to);
        return this;
    }

    public changeAttendedAllDay(from: boolean, to: boolean) {
        this.attendedAllDayChange = this.asBooleanChange(from, to);
        return this;
    }

    public override hasChanges(): boolean {
        return this.cancelledChange != null || this.attendedAllDayChange != null;
    }

    public toDto(): GroupActivityAttendanceCommandDto {
        return ({
                    uuid: this.getUuid().toString(),
                    commandUri: this.commandUri,
                    timestamp: this.timestamp,
                    operation: this.operation,
                    activityUuid: this.activityUuid.toString(),
                    activityId: this.activityId,
                    referralId: this.referralId,
                    attendedAt: this.attendedAt.formatIso8601(),
                    cancelled: this.cancelledChange,
                    attendedAllDay: this.attendedAllDayChange
            });
    }
}
