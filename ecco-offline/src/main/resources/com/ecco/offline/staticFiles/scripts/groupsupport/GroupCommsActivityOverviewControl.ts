import GroupActivityOverviewControl from "./GroupActivityOverviewControl";
import {GroupActivityDto, SessionData} from "ecco-dto";
import {GroupCommsActivityClientListControl} from "./GroupCommsActivityClientListControl";
import {groupCommsActivityOptions} from "./GroupCommsActivityList";

class GroupCommsActivityOverviewControl extends GroupActivityOverviewControl {

    private commsOptions = groupCommsActivityOptions;

    protected options() {
        return this.commsOptions;
    }

    protected label(lookup: string) {
        switch (lookup) {
            case "invited":
                return "register";
            case "attending":
                return "scheduled";
            case "attended":
                return "sent";
            case "attendance": // only when end date, which is no more
                return "attendance";
            case "register":
                return "printable";
            default:
                return lookup;
        }
    }

    protected createClientListControl(sessionData: SessionData, activity: GroupActivityDto) {
        return new GroupCommsActivityClientListControl(sessionData, activity, this.options());
    }

}
export default GroupCommsActivityOverviewControl;