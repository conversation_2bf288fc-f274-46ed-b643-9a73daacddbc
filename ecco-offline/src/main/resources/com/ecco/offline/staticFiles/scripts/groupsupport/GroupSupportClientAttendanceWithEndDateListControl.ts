import $ = require("jquery");
import moment = require("moment");

import ActionButton from "../controls/ActionButton";
import BaseAsyncTableControl from "../controls/BaseAsyncTableControl";
import BaseTableRowControl from "../controls/BaseTableRowControl";
import * as commands from "./commands";
import {EccoDate, StringToObjectMap} from "@eccosolutions/ecco-common";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {CommandQueue} from "ecco-commands";
import {apiClient} from "ecco-components";
import {ClientAttendance, ClientAttendanceDto, DailyClientAttendanceDto, GroupActivity, GroupSupportAjaxRepository, SessionDataAjaxRepository} from "ecco-dto";
import {getCommandQueueRepository} from "ecco-offline-data";

const repository = new GroupSupportAjaxRepository(apiClient);
const sessionDataRepository = new SessionDataAjaxRepository(apiClient);

class RowControl extends BaseTableRowControl<ClientAttendance> {

    constructor(private fromDate: EccoDate, private toDate: EccoDate, private rangeDates: EccoDate[], clientAttendance: ClientAttendance,
            private onAttendanceChanged: (clientAttendance: ClientAttendanceDto, dailyAttendance: DailyClientAttendanceDto,
                attendedAllDay: boolean) => void) {
        super(clientAttendance);
    }

    protected override getColumnData(headerLabel: string) : $.JQuery | string | null {
        if (headerLabel) {
            const headerDate = EccoDate.fromMoment(moment(headerLabel, "DD MMMM YYYY"));
            if (this.isInAttendanceDateRange(headerDate)) {
                let dailyAttendance = this.item.getDailyAttendance(headerDate.formatIso8601());
                // provide default dto client side
                dailyAttendance = dailyAttendance
                    || {date: headerDate.formatIso8601(), attendedAllDay: null, attending: false, cancelled: null};

                return this.buildIcon(dailyAttendance);
            }
            return null;
        } else {
            return this.item.getDto().referralSummary.displayName
        }
    }

    protected override getColumnMapping(): StringToObjectMap<(dto: ClientAttendance) => string|$.JQuery> {
        const m: StringToObjectMap<(dto: ClientAttendance) => string|$.JQuery> = {};
        this.rangeDates.forEach(h => {
            // formatShort() is the same as BaseAsyncTableControl.getHeaderLabel
            m[h.formatShort()] = item => this.cellDisplayFor(h);
            return m;
        });
        return m;
    }

    private cellDisplayFor(date: EccoDate): $.JQuery | string {
        if (date) {
            const outsideRange = date.earlierThan(this.fromDate)
                || this.toDate && date.laterThan(this.toDate);
            return $("<span>")
                .addClass(outsideRange && "text-danger")
                .text( moment(date.toLocalJsDate()).format("ddd") );
        }
        return "";
    }

    private buildIcon(dailyAttendance: DailyClientAttendanceDto): $.JQuery {
        let attendedState = dailyAttendance.attendedAllDay;

        const $icon = $('<i>').addClass(this.buildIconClass(dailyAttendance.attending, dailyAttendance.attendedAllDay));
        $icon.on("click", () => {
            attendedState = !attendedState;
            this.onAttendanceChanged(this.item.getDto(), dailyAttendance, attendedState);
            $icon.removeClass().addClass(this.buildIconClass(dailyAttendance.attending, attendedState));
        });
        return $icon;
    }

    private buildIconClass(attending: boolean, attended: boolean): string {
        let className = "fa fa-lg";
        className += attended && " fa-check-circle" || " fa-times-circle";
        className += attended == true && " attended" || attended == false && " not-attended" || " unknown-attended";
        className += attending == true && " attending" || attending == false && " not-attending" || "";
        return className;
    }

    private isInAttendanceDateRange(date: EccoDate) {
        const startDate: EccoDate = this.item.getDto().referralSummary.receivingServiceDate && EccoDate.parseIso8601(this.item.getDto().referralSummary.receivingServiceDate);
        const endDate: EccoDate = this.item.getDto().referralSummary.exitedDate && EccoDate.parseIso8601(this.item.getDto().referralSummary.exitedDate);

        if (startDate && date.earlierThan(startDate)) return false;
        return !(endDate && date.laterThan(endDate));

    }
}

class GroupSupportClientAttendanceWithEndDateListControl extends BaseAsyncTableControl<ClientAttendance> {

    private commandQueue = new CommandQueue(getCommandQueueRepository());

    private $saveButton: $.JQuery;

    private fromDate: EccoDate;

    private toDate: EccoDate;

    private dateRange: EccoDate[];

    constructor(private activity: GroupActivity) {
        super();
    }

    protected fetchViewData(): Promise<ClientAttendance[]> {
        return sessionDataRepository.getSessionData() // cached
            .then(sessionData =>
                repository.findClientAttendancesByActivityIdWithEndDate(this.activity.getDto().id, this.fromDate, this.toDate)
                    .then( (attendances) => attendances.map( (dto) => new ClientAttendance(dto, sessionData) ) )
        );
    }

    protected createRowControl(clientAttendance: ClientAttendance) {
        return new RowControl(this.fromDate, this.toDate, this.dateRange, clientAttendance, this.onAttendanceChanged.bind(this));
    }

    protected getHeaders(): EccoDate[] {
        // null triggers the client name in 'getColumnData' RowControl above
        return [null, ...this.dateRange];
    }

    private hasChanges(items: ClientAttendance[]): boolean {
        let hasChanges = false;

        items.forEach(item => {
            item.getDto().dailyAttendances.forEach(value => {
                if (value.attendedAllDay !== null) {
                    hasChanges = true;
                }
            });
        });
        return hasChanges;
    }

    protected override render(items: ClientAttendance[]) {
        super.render(items);

        this.$saveButton = this.buildSaveButton();
        if (this.hasChanges(items)) {
            this.$saveButton.text("update");
        }
        else {
            this.append(this.buildAutoFillButton());
        }
        this.append(this.$saveButton);
    }

    public loadWithDateRange(fromDate: EccoDate, toDate: EccoDate) {
        this.fromDate = fromDate;
        this.toDate = toDate;
        this.dateRange = this.buildDateRange(fromDate, toDate);
        this.commandQueue.clear();
        super.load();
    }

    private buildAutoFillButton(): ActionButton {
        return new ActionButton(" auto fill")
            .addClass("btn btn-default pull-left")
            .iconClasses("fa fa-lg fa-times-circle unknown-attended attending")
            .clickSynchronous(() => {
                this.element().find("table i.unknown-attended.attending").click();
            });
    }

    private buildSaveButton(): $.JQuery {
        const $button = $("<button>");
        $button.text("save");
        $button.addClass("btn btn-primary pull-right");
        $button.prop("disabled", true);
        $button.on("click", this.onSave.bind(this));
        return $button;
    }

    private buildDateRange(fromDate: EccoDate, toDate: EccoDate): EccoDate[] {
        const range: EccoDate[] = [];
        let day = fromDate;
        while (day.earlierThanOrEqual(toDate)) {
            range.push(day);
            day = day.addDays(1);
        }
        return range;
    }

    private onAttendanceChanged(clientAttendance: ClientAttendanceDto, dailyAttendance: DailyClientAttendanceDto,
            attendedAllDay: boolean) {
        const cmd = new commands.GroupActivityAttendanceCommand("update",
            Uuid.parse(this.activity.getDto().uuid), this.activity.getDto().id, clientAttendance.referralSummary.referralId,
            EccoDate.parseIso8601(dailyAttendance.date));
        cmd.changeAttendedAllDay(dailyAttendance.attendedAllDay, attendedAllDay);
        this.commandQueue.addCommand(cmd);

        this.$saveButton.prop("disabled", this.commandQueue.size() == 0);
    }

    private onSave() {
        this.commandQueue.flushCommands().then(() => {
            super.load();
        });
    }
}

export default GroupSupportClientAttendanceWithEndDateListControl;
