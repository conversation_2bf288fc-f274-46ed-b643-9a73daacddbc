import BaseAsyncCommandForm from "../cmd-queue/BaseAsyncCommandForm";
import Form from "../controls/Form";
import InputGroup from "../controls/InputGroup";
import TextInput from "../controls/TextInput";
import * as commands from "./commands";
import {apiClient} from "ecco-components";
import {QuestionGroup as QuestionGroupDto, QuestionGroupAjaxRepository} from "ecco-dto";
import {showFormInModalDom} from "../components/MUIConverterUtils";

var repository = new QuestionGroupAjaxRepository(apiClient);


class EditQuestionGroupForm extends BaseAsyncCommandForm<QuestionGroupDto> {

    public static showInModal(questionGroupId: number) {
        var form = new EditQuestionGroupForm(questionGroupId);
        form.load();
        showFormInModalDom(form);
    }

    private form = new Form().addClass("form-30-50");
    private name = new TextInput("question group name");
    private origDto: QuestionGroupDto;

    /** We provide the form with current state, and we get back a CommandQueue
     *  containing the changes to send/apply (if we click done).
     */
    constructor(private questionGroupId: number) {
        super(!questionGroupId ? "add new question group" : "edit question group");
        this.form
            .append( new InputGroup("question group name", this.name) );
    }

    protected fetchViewData(): Promise<QuestionGroupDto> {
        if (this.questionGroupId) {
            return repository.findOneQuestionGroup(this.questionGroupId);
        }
        return Promise.resolve(null);
    }

    protected render(questionGroupDto: QuestionGroupDto) {
        this.origDto = questionGroupDto;

        if (questionGroupDto) {
            this.name.setVal(questionGroupDto.name);
        }

        this.enableSubmit(); // probably want to have this be linked to length of commandQueue (based on event?)
        this.element().empty();
        this.append(this.form);
    }


    protected override submitForm(): Promise<void> {
        var cmd;
        if (this.origDto) {
            cmd = new commands.QuestionGroupChangeCommand("update", this.origDto.id)
                .changeName(this.origDto.name, this.name.val());
        }
        else {
            cmd = new commands.QuestionGroupChangeCommand("add", null)
                .changeName(null, this.name.val());
        }

        if (cmd.hasChanges()) {
            this.commandQueue.addCommand(cmd);
        }
        return super.submitForm();
    }

}

export default EditQuestionGroupForm;
