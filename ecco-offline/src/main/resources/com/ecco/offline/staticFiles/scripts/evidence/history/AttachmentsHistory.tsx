import $ = require("jquery");
import services = require("ecco-offline-data");
import BaseAsyncDataControl from "../../controls/BaseAsyncDataControl";
import QuestionnaireHistoryItemControl from "../questionnaire/QuestionnaireHistoryItemControl";
import RiskHistoryItemControl from "../risk/RiskHistoryItemControl";
import SupportHistoryItemControl from "../support/SupportHistoryItemControl";
import * as React from "react"
import {FC, useMemo} from "react"
import {Uuid} from "@eccosolutions/ecco-crypto";
import {QuestionnaireWork, RiskWork as RiskWorkDom, SupportWork as SupportWorkDom} from "ecco-commands";
import {useReloadHandler} from "ecco-components";
import {
    BaseWork,
    EvidenceAttachment,
    QuestionnaireWorkDto,
    RiskWorkEvidenceDto,
    ServiceRecipientAjaxRepository,
    ServiceRecipientWithEntities,
    SupportWork as SupportWorkDto,
    TaskEvidenceType
} from "ecco-dto";
import ControlWrapper from "../../components/ControlWrapper";
import {onParentTabActivated} from "../../common/tabEvents";
import {BaseHistoryItemControl} from "../BaseHistoryItemControl";
import {apiClient} from "ecco-components";

const srRepository = new ServiceRecipientAjaxRepository(apiClient);

interface Props {
    serviceRecipientId: number;
}

/* @Exemplar */
export const AttachmentsHistory: FC<Props> = props => {
    const control = useMemo(() => new EvidenceHistoryControl(props.serviceRecipientId), [props.serviceRecipientId]);
    useReloadHandler(() => control.load());
    const $tab = $("#tab-attachments");
    onParentTabActivated($tab, () => {
        control.load();
    });
    return <ControlWrapper control={control} dontLoad={$tab.length > 0}/>
};

class BackingData {
    constructor(public nextControls: BaseHistoryItemControl<any>[]) {}
}

class EvidenceHistoryControl extends BaseAsyncDataControl<BackingData> {

    private $wrapper: $.JQuery = $("<div>");
    // initial state - leave the spinner
    private $ul = $("<ul>").addClass("entry-list list-unstyled");
    private allWorkLinks: EvidenceAttachment[];
    private sr: ServiceRecipientWithEntities;
    private $moreBtn: $.JQuery;
    protected pageNumber = 0;
    private pageSize: number;
    private lastControlCount = 0;
    protected controls: BaseHistoryItemControl<any>[] = [];

    constructor(private serviceRecipientId: number) {
        super();
    }

    // NB next time, refactor as native react, possibly with some infinite scrolling - see CollectionSubscription as per BaseHistoryListControl
    protected fetchViewData(): Promise<BackingData> {
        const sreQ = !this.sr
                ? services.getReferralRepository().findOneServiceRecipientWithEntities(this.serviceRecipientId)
                : Promise.resolve(this.sr);
        const allWorkQ = !this.allWorkLinks
                ? srRepository.findServiceRecipientAttachments(this.serviceRecipientId)
                : Promise.resolve(this.allWorkLinks);

        return sreQ.then(sr => {
            this.sr = sr;

            return allWorkQ.then(workLinks => {
                this.allWorkLinks = workLinks;

                this.pageSize = parseInt(sr.features.getSetting("com.ecco.evidence:pageSize.history") || "20");
                const nextLinks = workLinks.slice(this.pageNumber * this.pageSize, (this.pageNumber * this.pageSize) + this.pageSize);
                // get work was null if it was from supportStaffNotes (api restricted to /needs), but we've restricted the server's workLinks
                const qLinks = apiClient.fetchRelations<BaseWork>(nextLinks, "work");
                return qLinks.then(works => {
                    const nextControls: BaseHistoryItemControl<any>[] = [];
                    works.forEach(w => {
                        const type = workLinks.filter(e => e.workUuid == w.id).pop().taskEvidenceType;
                        const ctl = this.createItemControl(type, w);
                        nextControls.push(ctl);
                    });
                    return new BackingData(nextControls);
                });
            });
        });
    }

    protected render(data: BackingData) {
        const isFirstRender = this.pageNumber == 0;

        if (isFirstRender) {
            this.$ul.empty();
            this.$wrapper.empty().append(this.$ul);
            this.element().empty().append(this.$wrapper);
        }

        data.nextControls.forEach(ctl => {
            this.controls.push(ctl);
            this.$ul.append(ctl.element());
        })

        if (this.controls.length == 0) {
            this.$ul.append($("<li>").text("no history recorded"));

        // COPY of BaseHistoryItemControl
        // NOTE: This will show 'more...' if there are exactly the page size entries even if there are no more entries
        // We may never get around to fixing this given the effort vs reward
        } else if ((this.controls.length % this.pageSize == 0)) {
            const noMoreData = this.lastControlCount == this.controls.length;
            if (!noMoreData) {
                this.$moreBtn = $("<li>").addClass("text-center")
                        .append($("<button>").addClass("btn btn-link")
                                .text("more...")
                                .click(() => {
                                    this.pageNumber++;
                                    this.loadMore();
                                }));
                this.$ul.append(this.$moreBtn);
            }
        }
    }

    private loadMore() {
        this.$moreBtn.remove();
        this.lastControlCount = this.controls.length;
        super.load(false);
    }

    private createItemControl(t: TaskEvidenceType, w: BaseWork): BaseHistoryItemControl<any> {
        switch (t) {
            case "EVIDENCE_SUPPORT":
                return new SupportHistoryItemControl(this.sr, new SupportWorkDom(Uuid.parse(w.id), w as SupportWorkDto), undefined, true);
            case "EVIDENCE_RISK":
                return new RiskHistoryItemControl(this.sr, new RiskWorkDom(Uuid.parse(w.id), w as RiskWorkEvidenceDto), undefined, true);
            case "EVIDENCE_QUESTIONNAIRE":
                return new QuestionnaireHistoryItemControl(this.sr, new QuestionnaireWork(Uuid.parse(w.id), w as QuestionnaireWorkDto), undefined, true);
        }
        throw new Error("unknown task evidence type " + t);
    }
}

export default AttachmentsHistory;
