import $ = require("jquery");

import BaseControl from "../controls/BaseControl";
import * as ReactDom from "react-dom"
import * as React from "react"
import {EvidenceAssociatedContactCommand, CommandQueue} from "ecco-commands";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {Col, Row} from "react-bootstrap";
import {AttendanceStatus, isOffline, ServiceType, SessionData} from "ecco-dto";
import {EvidenceDef} from "ecco-evidence";
import {TimerComponent, TimerEvent} from "ecco-components";
import {bus} from "@eccosolutions/ecco-common";

const timerEvents = bus<TimerEvent>();

export class TimerControl extends BaseControl {

    constructor() {
        super();
    }

    render() {
        const $content = $("<div>");
        this.element().empty().append($content);

        ReactDom.render(
            // NB we don't need any data from, eg <SessionDataController>, that produces contexts
            <div>
                <TimerComponent/>
            </div>
            , $content[0]);
    }
}

/**
 * A control to be placed on an evidence page in place of datetime.
 * However, we are moving towards an app-approach, so unlikely this will be used.
 */
export class LoneWorkerControlArchived extends BaseControl {

    private component: LoneWorkerComponent;

    constructor(private sessionData: SessionData,
                private workUuid: Uuid,
                private serviceRecipientId: number,
                private contactId: number,
                private taskName: string,
                private serviceType: ServiceType) {
        super();
    }

    createCommand(status: AttendanceStatus): EvidenceAssociatedContactCommand {
        const evidenceDef = EvidenceDef.fromTaskName(this.sessionData, this.serviceType, this.taskName);
        const cmd = new EvidenceAssociatedContactCommand(this.workUuid,
            this.serviceRecipientId, this.contactId,
            evidenceDef.getEvidenceGroup().name, this.taskName);
        cmd.attendanceStatus = status;
        //cmd.plannedDuration
        //cmd.eventId
        //cmd.location
        return cmd;
    }

    submitLoneWorking(status: AttendanceStatus, onSuccess: () => void, onFailure: () => void) {
        new CommandQueue()
            .addCommand(this.createCommand(status))
            .flushCommands()
            // .then(() => BaseAsyncDataControl.showNoticeAndFadeAway(this.element(), "alert-info", "change saved"))
            // .then(() => this.onSavedBus.fire())
            .then(() => onSuccess())
            .catch(() => {
                onFailure();
            });
    }

    render() {
        const $content = $("<div>");
        this.element().empty().append($content);

        const submitLoneWorkeringThisWrapper: (status: AttendanceStatus, onSuccess: () => void, onFailure: () => void) => void
            = (status: AttendanceStatus, onSuccess: () => void, onFailure: () => void) => {
                return this.submitLoneWorking(status, onSuccess, onFailure);
            };

        ReactDom.render(
                // NB we don't need any data from, eg <SessionDataController>, that produces contexts
                <div>
                    <TimerComponent/>
                    {getLoneWorkerComponent(this.serviceRecipientId, submitLoneWorkeringThisWrapper, ref => {this.component = ref})}
                </div>
                , $content[0]);
    }
}

export function getLoneWorkerComponent(serviceRecipientId: number,
                                       submitCommand: (status: AttendanceStatus, onSuccess: () => void, onFailure: () => void) => void,
                                       formRef: (c: LoneWorkerComponent) => void) {
    return (
            // NB we don't need any contexts here
            <LoneWorkerComponent
                offline={isOffline()}
                submitCommand={submitCommand}
                ref={formRef}
            />
    );
}


interface LoneProps extends React.ClassAttributes<LoneWorkerComponent> {
    offline: boolean;
    submitCommand: (status: AttendanceStatus, onSuccess: () => void, onFailure: () => void) => void
}

interface LoneState {
    loneWorkerActive: boolean,
    loneWorkerError: boolean
}

export class LoneWorkerComponent extends React.Component<LoneProps, LoneState>  {

    constructor(props) {
        super(props);
        this.state = {
            loneWorkerActive: false,
            loneWorkerError: false
        };

        timerEvents.addHandler( event => event.running ? this.start() : this.stop());
    }

    start() {
        const status = AttendanceStatus.START;
        this.props.submitCommand(status, () => this.loneWorkerStatus(status), () => this.loneWorkerError());
    }

    stop() {
        const status = AttendanceStatus.END;
        this.props.submitCommand(status, () => this.loneWorkerStatus(status), () => this.loneWorkerError());
    }

    loneWorkerStatus(status: AttendanceStatus) {
        this.setState(() => ({
            loneWorkerActive: status == AttendanceStatus.START
        }));
    }

    loneWorkerError() {
        this.setState(() => ({
            loneWorkerActive: false,
            loneWorkerError: true
        }));
    }

    override render() {
        return (
                <Row>
                    <Col>
                        lone-worker status: {
                            this.props.offline
                                ? "offline - please use the phones"
                                : this.state.loneWorkerActive
                                    ? "active"
                                    : "problem - please use phones"}
                    </Col>
                </Row>
        );
    }

}
