import $ = require("jquery");
import SelectList from "../controls/SelectList";
import {ServiceDto} from "ecco-dto";
import {getGlobalEccoAPI} from "ecco-components";

const EMPTY_ENTRY_KEY = "-1";

interface ServiceProject {
    serviceId: number;
    projectId: number;
}

/** Turns -1 into null for cleaner coding */
function asNumberOrNull(str: string) {
    var n = str ? parseInt(str) : null;
    return n == -1 ? null : n;
}

// shown in controls/ReferralsListControl - list by status / group activity / incident choosing nearest service
class ServiceProjectSelectionControl {

    private $container = $("<div>");

    private servicesSelect: SelectList;
    private projectsSelect: SelectList;

    constructor(private servicesProjects: ServiceDto[], private initialValue: ServiceProject,
            private onChange: (updatedSelection: ServiceProject) => void, private emptyEntryText = "-") {

        this.render();
        this.populateDropDowns();
    }

    public getSelectedService(): number {
        return asNumber<PERSON>rNull(this.servicesSelect.selected(false));
    }

    public getSelectedProject(): number {
        return asNumberOrNull(this.projectsSelect.selected(false));
    }

    /* Creates the drop down lists for services/projects */
    private populateDropDowns() {
        // TODO: I'm here - both onChangeXX here affect services list .. this needs converting to React

        // don't show disabled services (see class comment for usages) - NB populateFromList filters out hidden
        this.servicesSelect.populateFromList(this.servicesProjects,
            service => ({ key: service.id.toString(), value: service.name, isHidden: service.disabled}),
            service => service.id == this.initialValue.serviceId);
        if (this.initialValue.serviceId) {
            this.onChangeService(this.initialValue.serviceId.toString());

        }
    }

    /* Creates the drop down lists for services/projects */
    private render() {
        const messages = getGlobalEccoAPI().sessionData.getMessages()
        this.$container.empty();

        this.createSelectLists();

        const $rowSrv = $("<p>").attr("class", "e-row")
            .append(
                $("<span>").attr("class", "e-label").text("service ")
            ).append(
                $("<span>").attr("class", "input").append(this.servicesSelect.element())
            );
        const $rowPrj = $("<p>").attr("class", "e-row")
            .append(
                $("<span>").attr("class", "e-label").text(messages.project + " ")
            ).append(
                $("<span>").attr("class", "input").append(this.projectsSelect.element())
            );
        this.$container
            .append($rowSrv)
            .append($rowPrj);
    }

    private createSelectLists(): void {

        this.servicesSelect = new SelectList("services", true, this.emptyEntryText)
            .noTriggerChangeOnPopulate()
            .change(id => this.onChangeService(id));

        this.projectsSelect = new SelectList("projects", true, this.emptyEntryText)
            .noTriggerChangeOnPopulate()
            .change(id => this.notify());
    }

    private notify() {
        this.onChange( {
                serviceId: this.getSelectedService(),
                projectId: this.getSelectedProject()
            });
    }

    /**
     * Changing a service updates the project drop down and indicates an .onChange callback
     */
    private onChangeService(id: string) {

        this.projectsSelect.clear();

        if (EMPTY_ENTRY_KEY == id) { // handle empty -> null
            this.notify();
            return;
        }

        // build an array of what can be seen
        // REVIEW - this could be dom-rich by creating a dom for the dto?
        this.servicesProjects.some( service => {
            // compare strings as javascript can't compare longs exactly, only integers
            // http://stackoverflow.com/questions/17320706/javascript-long-integer
            if (service.id.toString() == id) {
                // assign what can be seen
                const projects = getGlobalEccoAPI().sessionData.getServiceCategorisationProjects(service.id, true);
                this.projectsSelect.populateFromList(projects,
                    project => ({ key: project.id.toString(), value:project.name}),
                    item => item.id == this.initialValue.projectId);
                this.notify();
                return true;
            }
            return false;
        });
    }

    public element() {
        return this.$container;
    }
}
export default ServiceProjectSelectionControl;
