package com.ecco.acceptancetests.api.serviceConfig;

import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.evidence.EvidenceTask;
import com.ecco.serviceConfig.viewModel.ServiceTypeViewModel;
import com.ecco.test.support.UniqueDataService;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.serviceConfig.ServiceTypeChangeCommandViewModel;
import com.ecco.webApi.serviceConfig.TaskDefinitionEntryCommandViewModel;
import com.ecco.webApi.serviceConfig.TaskDefinitionEntrySettingCommandViewModel;
import com.ecco.webApi.serviceConfig.TaskDefinitionEntrySettingSpecificOutcomesByIdHandler;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.web.client.RestClientResponseException;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertThrows;

public class ServiceTypeChangeCommandAPITests extends BaseJsonTest {

    private static String ADD = BaseCommandViewModel.OPERATION_ADD;
    private static String UPDATE = BaseCommandViewModel.OPERATION_UPDATE;
    private static String REMOVE = BaseCommandViewModel.OPERATION_REMOVE;

    private static String[] serviceTypeNames = {
            UniqueDataService.instance.nameFor("to add"),
            UniqueDataService.instance.nameFor("to remove"),
            UniqueDataService.instance.nameFor("to update"),
            UniqueDataService.instance.nameFor("updated"),
            UniqueDataService.instance.nameFor("with outcome")};

    @BeforeEach
    public void cleanBefore() {
        clean(serviceTypeNames);
    }

    @AfterEach
    public void cleanAfter() {
        clean(serviceTypeNames);
    }

    @Test
    public void canAdd() {
        canAdd(serviceTypeNames[0]);
    }

    // HTTP 400 - Bad Request (Valid command but failed to execute) thrown by spring-data when deleting something that doesn't exist
    @Test
    public void cannotRemoveWhatIsNotThere() {
        int unknownId = -5;
        ServiceTypeChangeCommandViewModel vm = new ServiceTypeChangeCommandViewModel(REMOVE, unknownId);
        assertThrows(RestClientResponseException.class, () -> commandActor.executeCommand(vm));
    }

    @Test
    public void canAddThenRemove() {
        ServiceTypeViewModel vm = canAdd(serviceTypeNames[1]);

        ServiceTypeChangeCommandViewModel cmd = new ServiceTypeChangeCommandViewModel(REMOVE, vm.id);
        commandActor.executeCommand(cmd);

        List<ServiceTypeViewModel> filtered = serviceTypeActor.getServiceType(vm.name);
        assertThat(filtered).hasSize(0);
    }

    @Test
    public void canAddThenUpdate() {
        ServiceTypeViewModel vm = canAdd(serviceTypeNames[2]);
        List<ServiceTypeViewModel> filtered = serviceTypeActor.getServiceType(vm.name);
        assertThat(filtered).hasSize(1);
        assertThat(filtered.get(0).id).isEqualTo(vm.id);

        String newName = serviceTypeNames[3];
        ServiceTypeChangeCommandViewModel cmd = new ServiceTypeChangeCommandViewModel(UPDATE, vm.id);
        cmd.nameChange = ChangeViewModel.create(vm.name, newName);
        commandActor.executeCommand(cmd);

        filtered = serviceTypeActor.getServiceType(newName);
        assertThat(filtered).hasSize(1);
        assertThat(filtered.get(0).id).isEqualTo(vm.id);
        assertThat(filtered.get(0).name).isEqualTo(newName);
    }

    @Test
    public void canAddWithOutcome() {
        ServiceTypeViewModel vm = canAdd(serviceTypeNames[4]);
        List<ServiceTypeViewModel> filtered = serviceTypeActor.getServiceType(vm.name);
        assertThat(filtered).hasSize(1);
        assertThat(filtered.get(0).id).isEqualTo(vm.id);

        // add 'threatAssessmentReduction'
        var taskDefCmd = new TaskDefinitionEntryCommandViewModel(TaskDefinitionEntryCommandViewModel.OPERATION_ADD, vm.id, EvidenceTask.THREAT_ASSESSMENT_REDUCTION.getTaskName());
        taskDefCmd.orderbyChange = ChangeViewModel.changeNullTo(15);
        commandActor.executeCommand(taskDefCmd);

        // add 'outcomesById' for 70,71.73 threat outcomes
        var allThreats = Objects.requireNonNull(outcomeActor.findAllThreatOutcomes().getBody());
        var threats = Arrays.stream(allThreats).filter(o -> o.id >= 70).toList();
        var csvIds = threats.stream().map(t -> t.id.toString()).collect(Collectors.joining(","));
        var taskDefSettingCmd = new TaskDefinitionEntrySettingCommandViewModel(vm.id, EvidenceTask.THREAT_ASSESSMENT_REDUCTION.getTaskName(), TaskDefinitionEntrySettingSpecificOutcomesByIdHandler.OUTCOMESBYID);
        taskDefSettingCmd.valueChange = ChangeViewModel.changeNullTo(csvIds);
        commandActor.executeCommand(taskDefSettingCmd);

        // check the result
        filtered = serviceTypeActor.getServiceType(vm.name);
        assertThat(filtered).hasSize(1);
        assertThat(filtered.get(0).riskAreas.size()).isEqualTo(threats.size());
    }

    private ServiceTypeViewModel canAdd(String name) {
        return serviceTypeActor.createServiceType(name, false);
    }

    private void clean(String[] serviceTypeNames) {
        // should get partial matches, to avoid exact thread names?
        serviceTypeActor.removeServiceTypes(serviceTypeNames);
    }

}
