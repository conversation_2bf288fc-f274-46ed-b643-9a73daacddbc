package com.ecco.service;

import com.ecco.buildings.dom.FixedContainer;
import com.ecco.buildings.viewModel.BuildingViewModel;

import com.ecco.infrastructure.annotations.WriteableTransaction;
import org.jspecify.annotations.Nullable;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.data.domain.PageRequest;

import java.util.List;
import java.util.Optional;

/**
 * Service for managing building operations with proper transaction handling
 * and lazy loading resolution. Returns ViewModels to ensure proper session management.
 *
 * This service provides presentation layer operations, returning ViewModels and
 * handling the conversion within transactional boundaries.
 */
public interface BuildingService {

    /**
     * Find a building by external reference and return as ViewModel.
     *
     * @param externalRef the external reference
     * @return the building ViewModel, or null if not found
     */
    @Nullable
    Optional<BuildingViewModel> findByExternalRef(String externalRef);

    /**
     * Find all buildings by their IDs and return as ViewModels.
     *
     * @param buildingIds list of building IDs
     * @return list of building ViewModels
     */
    List<BuildingViewModel> findByIds(List<Integer> buildingIds);

    /**
     * Find all child buildings of a parent building and return as ViewModels.
     *
     * @param parentBuildingId the parent building ID
     * @return list of child building ViewModels
     */
    List<BuildingViewModel> findChildren(int parentBuildingId);

    /**
     * Find buildings with complex filtering options.
     *
     * @param showChildren whether to include child buildings/rooms
     * @param showDisabled whether to include disabled buildings
     * @param resourceTypes array of resource type names to filter by
     * @param addressLocationIds list of address location IDs to filter by
     * @param parentId parent building ID to filter by
     * @return list of building ViewModels matching the criteria
     */
    List<BuildingViewModel> findWithFilters(boolean showChildren, boolean showDisabled,
                                                             String[] resourceTypes, List<Integer> addressLocationIds,
                                                             Integer parentId);

    /**
     * Find care run buildings for a parent building.
     *
     * @param parentId the parent building ID
     * @param careRunResourceId the care run resource type ID
     * @return list of care run building ViewModels
     */
    List<BuildingViewModel> findCareRunViewModels(int parentId, int careRunResourceId);

    /**
     * Find buildings hierarchically (parent and all children).
     *
     * @param showChildren whether to include child buildings/rooms
     * @param showDisabled whether to include disabled buildings
     * @param resourceTypes array of resource type names to filter by
     * @param addressLocationIds list of address location IDs to filter by
     * @return list of building ViewModels in hierarchical order
     */
    List<BuildingViewModel> findBuildingViewModelsHierarchical(boolean showChildren, boolean showDisabled,
                                                              String[] resourceTypes, List<Integer> addressLocationIds);

    /**
     * Find a building and its children hierarchically by building ID.
     *
     * @param buildingId the building ID to start the hierarchy from
     * @return list of building ViewModels in hierarchical order (parent + all children)
     */
    List<BuildingViewModel> findBuildingViewModelsHierarchical(int buildingId);

    /**
     * Find buildings with pagination support.
     *
     * @param pageRequest the pagination request
     * @return list of building ViewModels for the requested page
     */
    List<BuildingViewModel> findBuildingViewModelsWithPagination(PageRequest pageRequest);

    /**
     * Update the location of a building.
     *
     * @param id the building ID
     * @param to the new location ID
     */
    @CacheEvict(value = "buildings", allEntries = true)
    void updateBuildingLocation(Integer id, Integer to);

    /**
     * Delete a building by ID.
     *
     * @param buildingId the building ID to delete
     */
    @WriteableTransaction
    void deleteBuilding(Integer buildingId);

    /**
     * Find a building by ID.
     *
     * @param buildingId the building ID
     * @return the building ViewModel, or null if not found
     */
    @Nullable
    Optional<BuildingViewModel> findById(Integer buildingId);

    /**
     * Find a building by service recipient ID.
     *
     * @param serviceRecipientId the service recipient ID
     * @return the building ViewModel, or null if not found
     */
    @Nullable
    Optional<BuildingViewModel> findByServiceRecipient_Id(int serviceRecipientId);

    /**
     * Find all buildings ordered by resource type name.
     *
     * @return list of building ViewModels ordered by resource type name
     */
    List<BuildingViewModel> findAllByOrderByResourceTypeNameAsc();

    /**
     * Find all buildings by resource type ID.
     *
     * @param resourceTypeId the resource type ID
     * @return list of building ViewModels with the specified resource type
     */
    List<BuildingViewModel> findAllByResourceTypeId(Integer resourceTypeId);

    /**
     * Find buildings by parent IDs, resource type, and enabled status.
     *
     * @param parentBuildingIds list of parent building IDs
     * @param resourceTypeId the resource type ID
     * @return list of enabled building ViewModels matching the criteria
     */
    List<BuildingViewModel> findAllByParentIdInAndResourceTypeIdAndDisabledFalse(
            List<Integer> parentBuildingIds, Integer resourceTypeId);

    // ShiftCareRunArchiveRotaHandler
    List<BuildingViewModel> findAll();

    /**
     * Save a building.
     *
     * @param building the building to save
     * @return the saved building ViewModel
     */
    @WriteableTransaction
    @CacheEvict(value = "buildings", allEntries = true)
    BuildingViewModel save(FixedContainer building);

}
