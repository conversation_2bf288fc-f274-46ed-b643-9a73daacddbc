import {CommentFormFields} from "ecco-dto";
import * as React from "react";
import {useState} from "react";
import {EccoTextInput, possiblyModalForm} from "ecco-components-core";
import {Grid} from "@eccosolutions/ecco-mui";

export function EvidenceCommentForm(props: {
    onCancel: () => void
    onSave: (commentForm: Partial<CommentFormFields>) => void
    value: Partial<CommentFormFields>
    autoSaveKey: string
}) {
    const [comment, setComment] = useState<string | null>(props.value.comment || null);

    return possiblyModalForm(
        "note",
        true,
        true,
        props.onCancel,
        () => props.onSave({comment: comment!}),
        false,
        false,
        <Grid container>
            <Grid item xs={12}>
                <EccoTextInput
                    data-test={"textarea-comment"}
                    propertyKey="comment"
                    label="comment"
                    value={comment}
                    onChange={setComment}
                    type="textarea"
                    rows={8}
                    autoSaveKey={props.autoSaveKey}
                />
            </Grid>
        </Grid>,
        "add"
    );
}