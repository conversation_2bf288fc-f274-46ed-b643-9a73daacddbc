// Makes application properties available via importing the module
// `application-properties`.
export = ApplicationProperties;

/** {@link ApplicationProperties} is available via the global variable
 * `applicationProperties`.
 *
 * This is only valid if the consuming project specifies
 * `"types": ["application-properties"]` in their `tsconfig.json`.
 *
 * Prefer to import the module `application-properties` if possible.
 *
 * @see ApplicationProperties */
export as namespace applicationProperties;

declare global {
    type ApplicationProperties = AppProperties;

    interface Window {
        /** {@link ApplicationProperties} is available via
         * `window.applicationProperties`.
         *
         * This is only valid if the consuming project specifies
         * `"types": ["application-properties"]` in their `tsconfig.json`.
         *
         * Prefer to import the module `application-properties` if possible.
         *
         * @see ApplicationProperties */
        applicationProperties: ApplicationProperties;
    }
}

/** Ecco application properties that are exposed to JavaScript.
 *
 * Use of this interface is complicated by our gradual move away from serving
 * ecco client apps via a Java Servlet and towards serving them as standalone
 * web apps that access ecco via the Web API.
 *
 * For ecco-offline, which is served via a Java Servlet, the JavaScript
 * implementation of this interface is generated dynamically in Java by
 * `com.ecco.offline.ApplicationPropertiesOfflineResourceProvider` and
 * corresponds to the Java interface
 * `com.ecco.infrastructure.config.ApplicationProperties`.
 *
 * For standalone web apps, this interface is generated at build time and
 * injected by the build system of the standalone app.
 *
 * In most contexts `applicationProperties` is available by importing the
 * module `application-properties` or by accessing
 * `window.applicationProperties`. If possible, importing the module should be
 * preferred.
 *
 * In both cases, projects should add `application-properties` as a dependency
 * to their `package.json`. Projects that access `window.applicationProperties`
 * should also add `"types": ["application-properties"]` to their
 * `tsconfig.json` to benefit from the ambient type declarations. */
declare namespace ApplicationProperties {
    /** The root path of the Ecco web application (the servlet context root e.g. "/uat/").
     *
     * The path ends with a slash ('/'), so it is possible to use it as a base
     * path against which to perform relative path resolution as defined by
     * RFC 3986 section 5.2.3. Note that this is different from the path
     * returned by javax.servlet.ServletContext#getContextPath,
     * which does not end with a slash. */
    export var applicationRootPath: string;

    /** The base URL for a remote host for e.g. Ecco API (e.g. "https://demo.eccosolutions.co.uk").
     *
     * If null, then the host is the same server as the application is served from.  This can then be combined
     * with applicationRoot path for the URL for the API e.g. `${remoteHost}${applicationRootPath}/api/`
     */
    export var remoteHost: string | undefined;

    /**  Cache-busted resource root path.
     *
     * The path ends with a slash ('/'), so it is possible to use it as a base
     * path against which to perform relative path resolution as defined by
     * RFC 3986 section 5.2.3. */
    export var resourceRootPath: string;

    /** Whether to load the user calendar events through the loginProvider. */
    export var isLoginProvidersOnly: boolean;
}

declare type ApplicationProperties = typeof ApplicationProperties;

// Alias for ApplicationProperties to avoid a circular reference in the declare global section.
declare type AppProperties = ApplicationProperties;