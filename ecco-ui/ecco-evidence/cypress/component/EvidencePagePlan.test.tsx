import {mount} from "cypress/react";
import * as React from "react";
import {EvidencePagePlanLayout, EvidencePageSetupForCommandForm} from "../../EvidencePage";
import {TestServicesContextProvider} from "ecco-components/test-support/TestServicesContextProvider";
import {sessionData, testEvidencePagePlanData} from "../../test-support/mockEvidence";
import {EccoAPI} from "ecco-components";
import {CommandForm} from "ecco-components";
import {CommandFormTest, CommandFormTestOutput} from "ecco-components/cmd-queue/testUtils";
import {Command} from "ecco-commands";
import {EvidencePageType} from "ecco-dto";

const overrides = {
    sessionData: sessionData
} as EccoAPI;

describe("EvidencePagePlan tests", () => {
    beforeEach(() => {
        // Mock API endpoints that the file upload component might call
        cy.intercept("GET", "**/api/evidence/service-recipients/*/evidence/*/attachments/", {
            statusCode: 200,
            body: []
        }).as("getAttachedFiles");

        cy.intercept("GET", "**/api/evidence/service-recipients/*/evidence/*/attachments/unused/", {
            statusCode: 200,
            body: []
        }).as("getUnattachedFiles");

        cy.intercept("POST", "**/api/secure/uploadHiddenJs.html*", req => {
            // Extract the filename from the FormData if possible
            const formData = req.body;
            let filename = "uploaded-file.pdf"; // fallback
            let size = 1024;
            let type = "application/pdf";

            // If we can extract file info from FormData, use it
            if (formData instanceof FormData) {
                const file = formData.get("file") as File;
                if (file) {
                    filename = file.name;
                    size = file.size;
                    type = file.type;
                }
            }

            req.reply({
                statusCode: 200,
                body: {
                    fileId: Math.floor(Math.random() * 1000) + 1, // Random ID
                    filename: filename,
                    size: size,
                    type: type,
                    links: []
                }
            });
        }).as("uploadFile");
    });

    it("mounts", () => {
        mount(
            <TestServicesContextProvider overrides={overrides}>
                <CommandFormTest>
                    {(form: CommandForm, cmdEmitted: Command[], cmdEmittedDraft: Command[]) => (
                        <>
                            {/* EvidencePageRoot - directly, when no commandForm required */}
                            {/* EvidencePageLoaderForCommandForm - when loading data */}
                            <EvidencePageSetupForCommandForm
                                initData={{
                                    ...testEvidencePagePlanData(
                                        EvidencePageType.assessmentReduction
                                    ),
                                    readOnly: false
                                }}
                            >
                                <EvidencePagePlanLayout />
                                <CommandFormTestOutput
                                    cmdEmitted={cmdEmitted}
                                    cmdEmittedDraft={cmdEmittedDraft}
                                />
                                {/* cmdEmitted here, and could show errors etc...*/}
                            </EvidencePageSetupForCommandForm>
                        </>
                    )}
                </CommandFormTest>
            </TestServicesContextProvider>
        );
        // breakpoints: xs, extra-small: 0px ; sm, small: 600px ; md, medium: 900px ; lg, large: 1200px ; xl, extra-large: 1536px.
        // for us, md is at 960
        cy.viewport(1200, 750);
    });
});
