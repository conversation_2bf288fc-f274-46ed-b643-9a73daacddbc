import * as React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { EvidenceFileUpload } from "../EvidenceFileUpload";
import { UploadResult } from "@eccosolutions/ecco-common";

// Mock the apiClient
jest.mock("ecco-components", () => ({
    apiClient: {
        get: jest.fn()
    }
}));

// Mock fetch
global.fetch = jest.fn();

describe("EvidenceFileUpload", () => {
    const mockUploadUrl = "/api/upload";
    const mockAttachedFilesUrl = "/api/attached";
    const mockOnFilesUploaded = jest.fn();
    const mockOnFilesRemoved = jest.fn();

    beforeEach(() => {
        jest.clearAllMocks();
        (fetch as jest.Mock).mockClear();
    });

    const defaultProps = {
        uploadUrl: mockUploadUrl,
        attachedFilesUrl: mockAttachedFilesUrl,
        onFilesUploaded: mockOnFilesUploaded,
        onFilesRemoved: mockOnFilesRemoved
    };

    it("renders upload area when not read-only", () => {
        render(<EvidenceFileUpload {...defaultProps} />);
        
        expect(screen.getByText("Drop files here or click to browse")).toBeInTheDocument();
        expect(screen.getByText("Choose Files")).toBeInTheDocument();
    });

    it("does not render upload area when read-only", () => {
        render(<EvidenceFileUpload {...defaultProps} readOnly={true} />);
        
        expect(screen.queryByText("Drop files here or click to browse")).not.toBeInTheDocument();
        expect(screen.queryByText("Choose Files")).not.toBeInTheDocument();
    });

    it("displays maximum file size", () => {
        const maxFileSize = 5242880; // 5MB
        render(<EvidenceFileUpload {...defaultProps} maxFileSize={maxFileSize} />);
        
        expect(screen.getByText("Maximum file size: 5 MB")).toBeInTheDocument();
    });

    it("handles file selection", async () => {
        const mockFile = new File(["test content"], "test.txt", { type: "text/plain" });
        
        (fetch as jest.Mock).mockResolvedValueOnce({
            ok: true,
            json: async () => ({
                fileId: 1,
                filename: "test.txt",
                size: 12,
                type: "text/plain",
                links: []
            } as UploadResult)
        });

        render(<EvidenceFileUpload {...defaultProps} />);
        
        const fileInput = screen.getByRole("button", { name: /choose files/i });
        const hiddenInput = document.querySelector('input[type="file"]') as HTMLInputElement;
        
        // Simulate file selection
        Object.defineProperty(hiddenInput, 'files', {
            value: [mockFile],
            writable: false,
        });
        
        fireEvent.change(hiddenInput);
        
        await waitFor(() => {
            expect(fetch).toHaveBeenCalledWith(mockUploadUrl, expect.objectContaining({
                method: 'POST',
                body: expect.any(FormData),
                credentials: 'same-origin'
            }));
        });
        
        await waitFor(() => {
            expect(mockOnFilesUploaded).toHaveBeenCalledWith([
                expect.objectContaining({
                    fileId: 1,
                    filename: "test.txt"
                })
            ]);
        });
    });

    it("validates file size", async () => {
        const maxFileSize = 1024; // 1KB
        const largeFile = new File(["x".repeat(2048)], "large.txt", { type: "text/plain" });
        
        render(<EvidenceFileUpload {...defaultProps} maxFileSize={maxFileSize} />);
        
        const hiddenInput = document.querySelector('input[type="file"]') as HTMLInputElement;
        
        Object.defineProperty(hiddenInput, 'files', {
            value: [largeFile],
            writable: false,
        });
        
        fireEvent.change(hiddenInput);
        
        await waitFor(() => {
            expect(screen.getByText(/file\(s\) too large/i)).toBeInTheDocument();
        });
        
        expect(fetch).not.toHaveBeenCalled();
        expect(mockOnFilesUploaded).not.toHaveBeenCalled();
    });

    it("handles drag and drop", async () => {
        const mockFile = new File(["test content"], "test.txt", { type: "text/plain" });
        
        (fetch as jest.Mock).mockResolvedValueOnce({
            ok: true,
            json: async () => ({
                fileId: 1,
                filename: "test.txt",
                size: 12,
                type: "text/plain",
                links: []
            } as UploadResult)
        });

        render(<EvidenceFileUpload {...defaultProps} />);
        
        const dropZone = screen.getByText("Drop files here or click to browse").closest('div');
        
        // Simulate drag over
        fireEvent.dragOver(dropZone!, {
            dataTransfer: {
                files: [mockFile]
            }
        });
        
        // Simulate drop
        fireEvent.drop(dropZone!, {
            dataTransfer: {
                files: [mockFile]
            }
        });
        
        await waitFor(() => {
            expect(fetch).toHaveBeenCalledWith(mockUploadUrl, expect.objectContaining({
                method: 'POST',
                body: expect.any(FormData),
                credentials: 'same-origin'
            }));
        });
    });

    it("displays uploaded files", async () => {
        const mockFiles: UploadResult[] = [
            {
                fileId: 1,
                filename: "document.pdf",
                size: 1024,
                type: "application/pdf",
                links: []
            },
            {
                fileId: 2,
                filename: "image.jpg",
                size: 2048,
                type: "image/jpeg",
                links: []
            }
        ];

        const { apiClient } = require("ecco-components");
        apiClient.get.mockResolvedValueOnce(mockFiles);

        render(<EvidenceFileUpload {...defaultProps} />);
        
        await waitFor(() => {
            expect(screen.getByText("document.pdf")).toBeInTheDocument();
            expect(screen.getByText("image.jpg")).toBeInTheDocument();
            expect(screen.getByText("Attached Files (2)")).toBeInTheDocument();
        });
    });

    it("handles file removal", async () => {
        const mockFiles: UploadResult[] = [
            {
                fileId: 1,
                filename: "document.pdf",
                size: 1024,
                type: "application/pdf",
                links: []
            }
        ];

        const { apiClient } = require("ecco-components");
        apiClient.get.mockResolvedValueOnce(mockFiles);

        render(<EvidenceFileUpload {...defaultProps} />);
        
        await waitFor(() => {
            expect(screen.getByText("document.pdf")).toBeInTheDocument();
        });

        const deleteButton = screen.getByRole("button", { name: /delete/i });
        fireEvent.click(deleteButton);
        
        expect(mockOnFilesRemoved).toHaveBeenCalledWith([1]);
    });

    it("formats file sizes correctly", () => {
        render(<EvidenceFileUpload {...defaultProps} maxFileSize={1073741824} />); // 1GB
        
        expect(screen.getByText("Maximum file size: 1 GB")).toBeInTheDocument();
    });

    it("handles upload errors", async () => {
        const mockFile = new File(["test content"], "test.txt", { type: "text/plain" });
        
        (fetch as jest.Mock).mockRejectedValueOnce(new Error("Network error"));

        render(<EvidenceFileUpload {...defaultProps} />);
        
        const hiddenInput = document.querySelector('input[type="file"]') as HTMLInputElement;
        
        Object.defineProperty(hiddenInput, 'files', {
            value: [mockFile],
            writable: false,
        });
        
        fireEvent.change(hiddenInput);
        
        await waitFor(() => {
            expect(screen.getByText(/upload failed/i)).toBeInTheDocument();
        });
    });
});
