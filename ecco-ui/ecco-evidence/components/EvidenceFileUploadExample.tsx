import * as React from "react";
import {FC, useCallback} from "react";
import {Box, Typography, Divider} from "@eccosolutions/ecco-mui";
import {EvidenceFileUpload} from "./EvidenceFileUpload";
import {UploadResult} from "@eccosolutions/ecco-common";
import {useEvidencePageContext} from "../EvidencePageRoot";
import {applicationRootPath} from "application-properties";

/**
 * Example component showing how to integrate EvidenceFileUpload into EvidencePage
 * This demonstrates the proper usage patterns and API integration
 */
export const EvidenceFileUploadExample: FC = () => {
    const {init, state, dispatch} = useEvidencePageContext();

    // Construct upload URLs based on the evidence context
    const serviceRecipientId = init.initData.serviceRecipientId;
    const evidenceGroupName = init.initData.evidenceDef.getEvidenceGroup().name;

    const uploadUrl = `${applicationRootPath}api/secure/uploadHiddenJs.html?source=service-recipient&serviceRecipientId=${serviceRecipientId}&evidenceGroupName=${evidenceGroupName}`;
    const unattachedFilesUrl = `${applicationRootPath}api/evidence/service-recipients/${serviceRecipientId}/evidence/${evidenceGroupName}/attachments/unused/`;
    const attachedFilesUrl = `${applicationRootPath}api/evidence/service-recipients/${serviceRecipientId}/evidence/${evidenceGroupName}/attachments/`;

    const handleFilesUploaded = useCallback(
        (files: UploadResult[]) => {
            console.log("Files uploaded successfully:", files);

            // Update the evidence page state with the new attachments
            // This would typically trigger a re-render of the evidence form
            dispatch({
                type: "updateAttachments",
                data: {
                    attachments: files
                }
            });

            // You might also want to show a success notification
            // showNotification("success", `${files.length} file(s) uploaded successfully`);
        },
        [dispatch]
    );

    const handleFilesRemoved = useCallback(
        (fileIds: number[]) => {
            console.log("Files removed:", fileIds);

            // Update the evidence page state to remove the attachments
            dispatch({
                type: "removeAttachments",
                data: {
                    fileIds
                }
            });

            // You might also want to show a notification
            // showNotification("info", `${fileIds.length} file(s) removed`);
        },
        [dispatch]
    );

    return (
        <Box sx={{p: 2}}>
            <Typography variant="h6" gutterBottom>
                Evidence Attachments
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{mb: 2}}>
                Upload files to support your evidence. Accepted formats include documents, images,
                and other relevant files.
            </Typography>

            <Divider sx={{mb: 2}} />

            <EvidenceFileUpload
                uploadUrl={uploadUrl}
                unattachedFilesUrl={unattachedFilesUrl}
                attachedFilesUrl={attachedFilesUrl}
                maxFileSize={10240000} // 10MB
                readOnly={init.initData.readOnly}
                onFilesUploaded={handleFilesUploaded}
                onFilesRemoved={handleFilesRemoved}
                acceptedFileTypes="image/*,.pdf,.doc,.docx,.txt,.rtf"
            />
        </Box>
    );
};

/**
 * Simplified version for basic usage without evidence context
 */
export const BasicEvidenceFileUpload: FC<{
    serviceRecipientId: number;
    evidenceGroupName: string;
    readOnly?: boolean;
}> = ({serviceRecipientId, evidenceGroupName, readOnly = false}) => {
    const uploadUrl = `${applicationRootPath}api/secure/uploadHiddenJs.html?source=service-recipient&serviceRecipientId=${serviceRecipientId}&evidenceGroupName=${evidenceGroupName}`;
    const unattachedFilesUrl = `${applicationRootPath}api/evidence/service-recipients/${serviceRecipientId}/evidence/${evidenceGroupName}/attachments/unused/`;
    const attachedFilesUrl = `${applicationRootPath}api/evidence/service-recipients/${serviceRecipientId}/evidence/${evidenceGroupName}/attachments/`;

    const handleFilesUploaded = useCallback((files: UploadResult[]) => {
        console.log("Files uploaded:", files);
        // Handle file upload success - could trigger parent component updates
    }, []);

    const handleFilesRemoved = useCallback((fileIds: number[]) => {
        console.log("Files removed:", fileIds);
        // Handle file removal - could trigger parent component updates
    }, []);

    return (
        <EvidenceFileUpload
            uploadUrl={uploadUrl}
            unattachedFilesUrl={unattachedFilesUrl}
            attachedFilesUrl={attachedFilesUrl}
            maxFileSize={10240000} // 10MB
            readOnly={readOnly}
            onFilesUploaded={handleFilesUploaded}
            onFilesRemoved={handleFilesRemoved}
            acceptedFileTypes="*/*"
        />
    );
};

/**
 * Integration example showing how to add file upload to an existing evidence form
 */
export const EvidenceFormWithFileUpload: FC<{
    children: React.ReactNode;
    serviceRecipientId: number;
    evidenceGroupName: string;
    readOnly?: boolean;
}> = ({children, serviceRecipientId, evidenceGroupName, readOnly = false}) => {
    return (
        <Box>
            {/* Existing evidence form content */}
            {children}

            {/* File upload section */}
            <Box sx={{mt: 3, pt: 2, borderTop: 1, borderColor: "divider"}}>
                <Typography variant="h6" gutterBottom>
                    Supporting Documents
                </Typography>
                <BasicEvidenceFileUpload
                    serviceRecipientId={serviceRecipientId}
                    evidenceGroupName={evidenceGroupName}
                    readOnly={readOnly}
                />
            </Box>
        </Box>
    );
};
