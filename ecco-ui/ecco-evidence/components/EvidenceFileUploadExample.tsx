import * as React from "react";
import {FC, useCallback} from "react";
import {Box, Typography, Divider, makeStyles, Theme} from "@eccosolutions/ecco-mui";
import {EvidenceFileUpload} from "./EvidenceFileUpload";
import {UploadResult} from "@eccosolutions/ecco-common";
import {useEvidencePageContext} from "../EvidencePageRoot";
import {useServicesContext} from "ecco-components";

const useStyles = makeStyles((theme: Theme) => ({
    container: {
        padding: theme.spacing(2)
    },
    description: {
        marginBottom: theme.spacing(2)
    },
    divider: {
        marginBottom: theme.spacing(2)
    },
    formWithUpload: {
        marginTop: theme.spacing(3),
        paddingTop: theme.spacing(2),
        borderTop: `1px solid ${theme.palette.divider}`
    },
    sectionTitle: {
        marginBottom: theme.spacing(1)
    }
}));

/**
 * Example component showing how to integrate EvidenceFileUpload into EvidencePage
 * This demonstrates the proper usage patterns and API integration
 */
export const EvidenceFileUploadExample: FC = () => {
    const classes = useStyles();
    const {init, state, dispatch} = useEvidencePageContext();
    const {apiClient} = useServicesContext();

    // Construct upload URLs based on the evidence context using the same pattern as ServicesContext
    const serviceRecipientId = init.initData.serviceRecipientId;
    const evidenceGroupName = init.initData.evidenceDef.getEvidenceGroup().name;

    // Use ApiClient's URL resolution - it uses new URL(path, baseUrl) internally
    const uploadUrl = new URL(
        `../secure/uploadHiddenJs.html?source=service-recipient&serviceRecipientId=${serviceRecipientId}&evidenceGroupName=${evidenceGroupName}`,
        apiClient.getWebApiUrl()
    ).href;
    const unattachedFilesUrl = new URL(
        `evidence/service-recipients/${serviceRecipientId}/evidence/${evidenceGroupName}/attachments/unused/`,
        apiClient.getWebApiUrl()
    ).href;
    const attachedFilesUrl = new URL(
        `evidence/service-recipients/${serviceRecipientId}/evidence/${evidenceGroupName}/attachments/`,
        apiClient.getWebApiUrl()
    ).href;

    const handleFilesUploaded = useCallback(
        (files: UploadResult[]) => {
            console.log("Files uploaded successfully:", files);

            // Update the evidence page state with the new attachments
            // This would typically trigger a re-render of the evidence form
            dispatch({
                type: "updateAttachments",
                attachments: files
            });

            // You might also want to show a success notification
            // showNotification("success", `${files.length} file(s) uploaded successfully`);
        },
        [dispatch]
    );

    const handleFilesRemoved = useCallback(
        (fileIds: number[]) => {
            console.log("Files removed:", fileIds);

            // Update the evidence page state to remove the attachments
            dispatch({
                type: "removeAttachments",
                fileIds: fileIds
            });

            // You might also want to show a notification
            // showNotification("info", `${fileIds.length} file(s) removed`);
        },
        [dispatch]
    );

    return (
        <Box className={classes.container}>
            <Typography variant="h6" gutterBottom>
                Evidence Attachments
            </Typography>
            <Typography variant="body2" color="textSecondary" className={classes.description}>
                Upload files to support your evidence. Accepted formats include documents, images,
                and other relevant files.
            </Typography>

            <Divider className={classes.divider} />

            <EvidenceFileUpload
                uploadUrl={uploadUrl}
                unattachedFilesUrl={unattachedFilesUrl}
                attachedFilesUrl={attachedFilesUrl}
                maxFileSize={10240000} // 10MB
                readOnly={init.initData.readOnly}
                onFilesUploaded={handleFilesUploaded}
                onFilesRemoved={handleFilesRemoved}
                acceptedFileTypes="image/*,.pdf,.doc,.docx,.txt,.rtf"
            />
        </Box>
    );
};

/**
 * Simplified version for basic usage without evidence context
 */
export const BasicEvidenceFileUpload: FC<{
    serviceRecipientId: number;
    evidenceGroupName: string;
    readOnly?: boolean;
}> = ({serviceRecipientId, evidenceGroupName, readOnly = false}) => {
    const {apiClient} = useServicesContext();

    // Use ApiClient's URL resolution - it uses new URL(path, baseUrl) internally
    const uploadUrl = new URL(
        `../secure/uploadHiddenJs.html?source=service-recipient&serviceRecipientId=${serviceRecipientId}&evidenceGroupName=${evidenceGroupName}`,
        apiClient.getWebApiUrl()
    ).href;
    const unattachedFilesUrl = new URL(
        `evidence/service-recipients/${serviceRecipientId}/evidence/${evidenceGroupName}/attachments/unused/`,
        apiClient.getWebApiUrl()
    ).href;
    const attachedFilesUrl = new URL(
        `evidence/service-recipients/${serviceRecipientId}/evidence/${evidenceGroupName}/attachments/`,
        apiClient.getWebApiUrl()
    ).href;

    const handleFilesUploaded = useCallback((files: UploadResult[]) => {
        console.log("Files uploaded:", files);
        // Handle file upload success - could trigger parent component updates
    }, []);

    const handleFilesRemoved = useCallback((fileIds: number[]) => {
        console.log("Files removed:", fileIds);
        // Handle file removal - could trigger parent component updates
    }, []);

    return (
        <EvidenceFileUpload
            uploadUrl={uploadUrl}
            unattachedFilesUrl={unattachedFilesUrl}
            attachedFilesUrl={attachedFilesUrl}
            maxFileSize={10240000} // 10MB
            readOnly={readOnly}
            onFilesUploaded={handleFilesUploaded}
            onFilesRemoved={handleFilesRemoved}
            acceptedFileTypes="*/*"
        />
    );
};

/**
 * Integration example showing how to add file upload to an existing evidence form
 */
export const EvidenceFormWithFileUpload: FC<{
    children: React.ReactNode;
    serviceRecipientId: number;
    evidenceGroupName: string;
    readOnly?: boolean;
}> = ({children, serviceRecipientId, evidenceGroupName, readOnly = false}) => {
    const classes = useStyles();

    return (
        <Box>
            {/* Existing evidence form content */}
            {children}

            {/* File upload section */}
            <Box className={classes.formWithUpload}>
                <Typography variant="h6" gutterBottom className={classes.sectionTitle}>
                    Supporting Documents
                </Typography>
                <BasicEvidenceFileUpload
                    serviceRecipientId={serviceRecipientId}
                    evidenceGroupName={evidenceGroupName}
                    readOnly={readOnly}
                />
            </Box>
        </Box>
    );
};
