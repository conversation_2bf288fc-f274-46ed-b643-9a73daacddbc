import * as React from "react";
import {FC, useCallback, useRef, useState, useEffect} from "react";
import {
    Box,
    Button,
    Card,
    CardContent,
    Chip,
    IconButton,
    LinearProgress,
    List,
    ListItem,
    ListItemIcon,
    ListItemSecondaryAction,
    ListItemText,
    Typography,
    Snackbar,
    Alert,
    makeStyles,
    Theme
} from "@eccosolutions/ecco-mui";
import {
    AttachFileIcon,
    DeleteIcon,
    CloudUploadIcon,
    InsertDriveFileIcon,
    ErrorIcon,
    CheckCircleIcon
} from "@eccosolutions/ecco-mui-controls";
import {apiClient} from "ecco-components";
import {UploadResult} from "@eccosolutions/ecco-common";

const useStyles = makeStyles((theme: Theme) => ({
    dropZone: {
        marginBottom: theme.spacing(2),
        border: '2px dashed #ccc',
        backgroundColor: 'transparent',
        cursor: 'pointer',
        transition: 'all 0.3s ease',
        '&:hover': {
            borderColor: theme.palette.primary.main,
        }
    },
    dropZoneActive: {
        border: '2px dashed #1976d2',
        backgroundColor: '#f3f9ff',
    },
    uploadContent: {
        textAlign: 'center',
        padding: theme.spacing(3),
    },
    uploadIcon: {
        fontSize: 48,
        color: theme.palette.text.secondary,
        marginBottom: theme.spacing(1),
    },
    uploadButton: {
        marginTop: theme.spacing(2),
    },
    hiddenInput: {
        display: 'none',
    },
    progressContainer: {
        marginBottom: theme.spacing(2),
    },
    fileListCard: {
        // No additional styles needed
    },
    fileListHeader: {
        marginBottom: theme.spacing(1),
    },
    fileItemSecondary: {
        display: 'flex',
        alignItems: 'center',
        gap: theme.spacing(1),
    },
    errorSnackbar: {
        width: '100%',
    }
}));

export interface EvidenceFileUploadProps {
    /** Upload endpoint URL */
    uploadUrl: string;
    /** URL to fetch unattached files */
    unattachedFilesUrl?: string;
    /** URL to fetch attached files */
    attachedFilesUrl?: string;
    /** Maximum file size in bytes */
    maxFileSize?: number;
    /** Whether the component is read-only */
    readOnly?: boolean;
    /** Callback when files are uploaded successfully */
    onFilesUploaded?: (files: UploadResult[]) => void;
    /** Callback when files are removed */
    onFilesRemoved?: (fileIds: number[]) => void;
    /** Accepted file types */
    acceptedFileTypes?: string;
}

// Removed FileUploadState interface - using individual useState hooks instead

export const EvidenceFileUpload: FC<EvidenceFileUploadProps> = ({
    uploadUrl,
    unattachedFilesUrl,
    attachedFilesUrl,
    maxFileSize = 10240000, // 10MB default
    readOnly = false,
    onFilesUploaded,
    onFilesRemoved,
    acceptedFileTypes = "*/*"
}) => {
    const classes = useStyles();
    const fileInputRef = useRef<HTMLInputElement>(null);
    const dropZoneRef = useRef<HTMLDivElement>(null);

    // Individual state hooks for better performance and clearer state management
    const [files, setFiles] = useState<UploadResult[]>([]);
    const [uploading, setUploading] = useState<boolean>(false);
    const [uploadProgress, setUploadProgress] = useState<number>(0);
    const [error, setError] = useState<string | null>(null);
    const [dragOver, setDragOver] = useState<boolean>(false);

    // Load existing attached files
    useEffect(() => {
        if (attachedFilesUrl) {
            loadAttachedFiles();
        }
    }, [attachedFilesUrl]);

    const loadAttachedFiles = useCallback(async () => {
        if (!attachedFilesUrl) return;

        try {
            const loadedFiles = await apiClient.get<UploadResult[]>(attachedFilesUrl);
            setFiles(loadedFiles);
        } catch (error) {
            console.error("Failed to load attached files:", error);
            setError("Failed to load existing files");
        }
    }, [attachedFilesUrl]);

    const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
        const files = event.target.files;
        if (files) {
            uploadFiles(Array.from(files));
        }
        // Reset input value to allow selecting the same file again
        if (fileInputRef.current) {
            fileInputRef.current.value = "";
        }
    }, []);

    const handleDragOver = useCallback((event: React.DragEvent) => {
        event.preventDefault();
        setDragOver(true);
    }, []);

    const handleDragLeave = useCallback((event: React.DragEvent) => {
        event.preventDefault();
        setDragOver(false);
    }, []);

    const handleDrop = useCallback((event: React.DragEvent) => {
        event.preventDefault();
        setDragOver(false);

        const files = Array.from(event.dataTransfer.files);
        if (files.length > 0) {
            uploadFiles(files);
        }
    }, []);

    const uploadFiles = useCallback(
        async (files: File[]) => {
            if (readOnly) return;

            // Validate file sizes
            const oversizedFiles = files.filter(file => file.size > maxFileSize);
            if (oversizedFiles.length > 0) {
                setError(`File(s) too large. Maximum size is ${formatFileSize(maxFileSize)}`);
                return;
            }

            setUploading(true);
            setUploadProgress(0);
            setError(null);

            try {
                const uploadPromises = files.map(file => uploadSingleFile(file));
                const results = await Promise.all(uploadPromises);

                const successfulUploads = results.filter(result => result && !result.error);
                const failedUploads = results.filter(result => !result || result.error);

                setFiles(prev => [...prev, ...successfulUploads]);
                setUploading(false);
                setUploadProgress(0);
                setError(failedUploads.length > 0 ? `${failedUploads.length} file(s) failed to upload` : null);

                if (successfulUploads.length > 0 && onFilesUploaded) {
                    onFilesUploaded(successfulUploads);
                }
            } catch (error) {
                console.error("Upload failed:", error);
                setUploading(false);
                setUploadProgress(0);
                setError("Upload failed. Please try again.");
            }
        },
        [readOnly, maxFileSize, onFilesUploaded]
    );

    const uploadSingleFile = useCallback(
        async (file: File): Promise<UploadResult | null> => {
            const formData = new FormData();
            formData.append("file", file);

            try {
                const response = await fetch(uploadUrl, {
                    method: "POST",
                    body: formData,
                    credentials: "same-origin"
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                return result as UploadResult;
            } catch (error) {
                console.error("Single file upload failed:", error);
                return {
                    filename: file.name,
                    size: file.size,
                    type: file.type,
                    error: "Upload failed",
                    links: []
                };
            }
        },
        [uploadUrl]
    );

    const handleRemoveFile = useCallback(
        (fileId: number) => {
            if (readOnly) return;

            setFiles(prev => prev.filter(file => file.fileId !== fileId));

            if (onFilesRemoved) {
                onFilesRemoved([fileId]);
            }
        },
        [readOnly, onFilesRemoved]
    );

    const formatFileSize = useCallback((bytes: number): string => {
        const sizes = ["B", "KB", "MB", "GB"];
        if (bytes === 0) return "0 B";
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round((bytes / Math.pow(1024, i)) * 10) / 10 + " " + sizes[i];
    }, []);

    const handleBrowseClick = useCallback(() => {
        if (!readOnly && fileInputRef.current) {
            fileInputRef.current.click();
        }
    }, [readOnly]);

    const closeError = useCallback(() => {
        setError(null);
    }, []);

    return (
        <Box>
            {/* Upload Area */}
            {!readOnly && (
                <Card
                    ref={dropZoneRef}
                    className={`${classes.dropZone} ${dragOver ? classes.dropZoneActive : ''}`}
                    onDragOver={handleDragOver}
                    onDragLeave={handleDragLeave}
                    onDrop={handleDrop}
                    onClick={handleBrowseClick}
                >
                    <CardContent className={classes.uploadContent}>
                        <CloudUploadIcon className={classes.uploadIcon} />
                        <Typography variant="h6" gutterBottom>
                            Drop files here or click to browse
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                            Maximum file size: {formatFileSize(maxFileSize)}
                        </Typography>
                        <Button
                            variant="outlined"
                            startIcon={<AttachFileIcon />}
                            className={classes.uploadButton}
                            onClick={handleBrowseClick}
                        >
                            Choose Files
                        </Button>
                    </CardContent>
                </Card>
            )}

            {/* Hidden file input */}
            <input
                ref={fileInputRef}
                type="file"
                multiple
                accept={acceptedFileTypes}
                className={classes.hiddenInput}
                onChange={handleFileSelect}
            />

            {/* Upload Progress */}
            {uploading && (
                <Box className={classes.progressContainer}>
                    <Typography variant="body2" gutterBottom>
                        Uploading files...
                    </Typography>
                    <LinearProgress />
                </Box>
            )}

            {/* File List */}
            {files.length > 0 && (
                <Card className={classes.fileListCard}>
                    <CardContent>
                        <Typography variant="h6" gutterBottom className={classes.fileListHeader}>
                            Attached Files ({files.length})
                        </Typography>
                        <List>
                            {files.map((file, index) => (
                                <ListItem key={file.fileId || index} divider>
                                    <ListItemIcon>
                                        {file.error ? (
                                            <ErrorIcon color="error" />
                                        ) : (
                                            <InsertDriveFileIcon />
                                        )}
                                    </ListItemIcon>
                                    <ListItemText
                                        primary={file.filename}
                                        secondary={
                                            <div className={classes.fileItemSecondary}>
                                                <Typography variant="caption">
                                                    {formatFileSize(file.size || 0)}
                                                </Typography>
                                                {file.error && (
                                                    <Chip
                                                        label="Failed"
                                                        size="small"
                                                        color="secondary"
                                                        variant="outlined"
                                                    />
                                                )}
                                                {!file.error && file.fileId && (
                                                    <Chip
                                                        label="Uploaded"
                                                        size="small"
                                                        color="primary"
                                                        variant="outlined"
                                                    />
                                                )}
                                            </div>
                                        }
                                    />
                                    {!readOnly && file.fileId && (
                                        <ListItemSecondaryAction>
                                            <IconButton
                                                edge="end"
                                                onClick={() => handleRemoveFile(file.fileId!)}
                                                size="small"
                                            >
                                                <DeleteIcon />
                                            </IconButton>
                                        </ListItemSecondaryAction>
                                    )}
                                </ListItem>
                            ))}
                        </List>
                    </CardContent>
                </Card>
            )}

            {/* Error Snackbar */}
            <Snackbar
                open={!!error}
                autoHideDuration={6000}
                onClose={closeError}
                anchorOrigin={{vertical: "bottom", horizontal: "center"}}
            >
                <Alert onClose={closeError} severity="error" className={classes.errorSnackbar}>
                    {error}
                </Alert>
            </Snackbar>
        </Box>
    );
};
