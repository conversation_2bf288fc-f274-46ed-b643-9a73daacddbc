# EvidenceFileUpload Component

A modern React component for uploading files in the Evidence system, built with
Material-UI and following the patterns established by the existing
`attachments.ts` functionality.

## Features

- **Drag & Drop Support**: Users can drag files directly onto the upload area
- **Multiple File Selection**: Support for selecting and uploading multiple
  files at once
- **Progress Indication**: Visual feedback during upload process with progress
  bars
- **File Validation**: Client-side validation for file size and type
  restrictions
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Read-only Mode**: Supports read-only mode for viewing existing attachments
- **Material-UI Integration**: Uses Material-UI components for consistent
  styling
- **Responsive Design**: Works well on desktop and mobile devices

## Basic Usage

```tsx
import {EvidenceFileUpload} from "ecco-evidence";

const MyComponent = () => {
    const handleFilesUploaded = (files: UploadResult[]) => {
        console.log("Files uploaded:", files);
    };

    const handleFilesRemoved = (fileIds: number[]) => {
        console.log("Files removed:", fileIds);
    };

    return (
        <EvidenceFileUpload
            uploadUrl="/api/secure/uploadHiddenJs.html?source=service-recipient&serviceRecipientId=123&evidenceGroupName=needs"
            attachedFilesUrl="/api/evidence/service-recipients/123/evidence/needs/attachments/"
            maxFileSize={10240000} // 10MB
            onFilesUploaded={handleFilesUploaded}
            onFilesRemoved={handleFilesRemoved}
            acceptedFileTypes="image/*,.pdf,.doc,.docx"
        />
    );
};
```

## Props

| Prop                 | Type                              | Default    | Description                                              |
| -------------------- | --------------------------------- | ---------- | -------------------------------------------------------- |
| `uploadUrl`          | `string`                          | Required   | The endpoint URL for uploading files                     |
| `unattachedFilesUrl` | `string?`                         | Optional   | URL to fetch previously uploaded but unattached files    |
| `attachedFilesUrl`   | `string?`                         | Optional   | URL to fetch currently attached files                    |
| `maxFileSize`        | `number`                          | `10240000` | Maximum file size in bytes (default: 10MB)               |
| `readOnly`           | `boolean`                         | `false`    | Whether the component is in read-only mode               |
| `onFilesUploaded`    | `(files: UploadResult[]) => void` | Optional   | Callback when files are successfully uploaded            |
| `onFilesRemoved`     | `(fileIds: number[]) => void`     | Optional   | Callback when files are removed                          |
| `acceptedFileTypes`  | `string`                          | `"*/*"`    | Accepted file types (HTML input accept attribute format) |

## Integration with EvidencePage

### Using with Evidence Context

```tsx
import {EvidenceFileUploadExample} from "ecco-evidence";

// Within an EvidencePage component that has access to useEvidencePageContext
const EvidencePageWithUpload = () => {
    return (
        <div>
            {/* Other evidence content */}
            <EvidenceFileUploadExample />
        </div>
    );
};
```

### Basic Integration

```tsx
import {BasicEvidenceFileUpload} from "ecco-evidence";

const MyEvidenceForm = ({serviceRecipientId, evidenceGroupName}) => {
    return (
        <div>
            {/* Form content */}
            <BasicEvidenceFileUpload
                serviceRecipientId={serviceRecipientId}
                evidenceGroupName={evidenceGroupName}
                readOnly={false}
            />
        </div>
    );
};
```

## API Endpoints

The component expects the following API endpoints to be available:

### Upload Endpoint

- **URL**: `/api/secure/uploadHiddenJs.html`
- **Method**: `POST`
- **Parameters**:
    - `source`: "service-recipient"
    - `serviceRecipientId`: The service recipient ID
    - `evidenceGroupName`: The evidence group name
- **Body**: `FormData` with file
- **Response**: `UploadResult` object

### Attached Files Endpoint

- **URL**:
  `/api/evidence/service-recipients/{serviceRecipientId}/evidence/{evidenceGroupName}/attachments/`
- **Method**: `GET`
- **Response**: Array of `UploadResult` objects

### Unattached Files Endpoint

- **URL**:
  `/api/evidence/service-recipients/{serviceRecipientId}/evidence/{evidenceGroupName}/attachments/unused/`
- **Method**: `GET`
- **Response**: Array of `UploadResult` objects

## File Size Formatting

The component includes a built-in file size formatter that displays sizes in
human-readable format:

- Bytes (B)
- Kilobytes (KB)
- Megabytes (MB)
- Gigabytes (GB)

## Error Handling

The component handles various error scenarios:

- File size exceeding the maximum limit
- Network errors during upload
- Server-side upload failures
- Invalid file types (when specified)

Errors are displayed using Material-UI's Snackbar component with appropriate
messaging.

## Styling

The component uses Material-UI v4's theming system with `makeStyles` for styling.
You can customize the appearance by overriding the theme or extending the styles.
Key visual elements include:

- Drag & drop area with visual feedback
- Progress indicators during upload
- File list with status indicators
- Error states with appropriate colors

## Accessibility

The component follows accessibility best practices:

- Proper ARIA labels and roles
- Keyboard navigation support
- Screen reader friendly
- High contrast support
- Focus management

## Browser Support

The component supports modern browsers with the following features:

- File API
- Drag and Drop API
- Fetch API
- FormData

For older browsers, appropriate polyfills may be required.

## Migration from attachments.ts

If you're migrating from the existing jQuery-based `attachments.ts`, here are
the key differences:

1. **React Hooks**: Uses modern React patterns instead of jQuery
2. **Material-UI**: Uses Material-UI components instead of Bootstrap
3. **TypeScript**: Full TypeScript support with proper typing
4. **Modern APIs**: Uses Fetch API instead of jQuery AJAX
5. **Component-based**: Encapsulated as a reusable React component

The API endpoints and data structures remain compatible with the existing
backend implementation.
